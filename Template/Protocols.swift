//
//  Protocols.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit


protocol transitionDelegate:NSObjectProtocol{
	func transitionComplete()
}

protocol controllerInputDelegate:NSObjectProtocol{
	func buttonPressed(_ buttonName:String/*controllerItem*/, button:MenuButtonNode?)
	func buttonReleased(_ buttonName:String/*controllerItem*/, button:MenuButtonNode?)
}

protocol mouseInputDelegate:NSObjectProtocol{
	func buttonPressedMouse(_ buttonName:String/*controllerItem*/, button:MenuButtonNode?)
	func buttonReleasedMouse(_ buttonName:String/*controllerItem*/, button:MenuButtonNode?)
	
	#if os(OSX)
		func mouseMoved(with event: NSEvent)
	#endif
}
//test
@objc protocol UIButtonDelegate:NSObjectProtocol{
	func onButtonDown()//_ index:Int, name:String)
	func onButtonUp()//_ index:Int, name:String)

	#if os(iOS)
		@objc optional func onButtonDown(_ touches: Set<UITouch>, with event: UIEvent?)
		@objc optional func onButtonMoved(_ touches: Set<UITouch>, with event: UIEvent?)
		@objc optional func onButtonUp(_ touches: Set<UITouch>, with event: UIEvent?)
	#endif
	
	#if os(OSX)
		@objc optional func onMouseEnter()
		@objc optional func onMouseExit()
	#endif
}

protocol newUIButtonDelegate:NSObjectProtocol{
	func onButtonUp();
	func onButtonDown();
}
