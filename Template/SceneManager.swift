//
//  SceneManager.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit


//struct GameScene{
//	
//	enum scenes:Int {
//		case menu = 0,help,settings,game
//	}
//	
//	static var currentScene:scenes = .menu
//	
//}

import GameKit

final class SceneManager{
	

    
    var mainSKView:SKView?
	var vc:GCEventViewController?
	var nextScene:SKScene?
	var previousScene:SKScene?
	var currentScene:SKScene?
	var theTransitionDelegate:transitionDelegate?
	
	
	static let shared = SceneManager()
	
	private init() {
		
	}
	
	func gotoScene(_ sceneName:String){
		
		let transition = SKTransition.fade(withDuration: 0.75)
		nextScene = SKScene(fileNamed: sceneName)
		theTransitionDelegate = nextScene as? transitionDelegate
		mainSKView?.presentScene(nextScene!,transition: transition)
		nextScene!.scaleMode = .aspectFit
		
	}
	
}
