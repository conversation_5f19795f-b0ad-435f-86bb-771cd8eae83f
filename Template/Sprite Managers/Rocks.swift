//
//  Rocks.swift
//  Aerolites
//
//  Created by <PERSON> on 11/20/15.
//  Copyright © 2015 <PERSON>. All rights reserved.
//

import SpriteKit


class Rocks: NSObject {
    
    
    
    let maxBigRocksForPool = 55
    var bigRockGroup: [Rock] = []
    var currentBigRock:Int = 0
    
    let maxMedRocksForPool = 55
    var medRockGroup: [Rock] = []
    var currentMedRock:Int = 0
    
    let maxSmlRocksForPool = 100
    var smlRockGroup: [Rock] = []
    var currentSmlRock:Int = 0
    
    
    override init() {
        super.init() // calls above mentioned controller with default name
        
		//DispatchQueue.global(qos: .utility).async{
			self.createRockPools()
		//}
		
    }
    
    
    func createRockPools(){
        
        //Big Rocks
        for _ in 1...maxBigRocksForPool {
            let ff = Rock(theSize:1, mass: CGFloat.random(in:0.501...0.701), imageNamed: getRandomRockTextureBySize(1))
            
            bigRockGroup.append(ff)//   insert(ff)
        }
        
        //Med Rocks
        for _ in 1...maxMedRocksForPool {
            let ff = Rock(theSize:2, mass: CGFloat.random(in:0.201...0.301), imageNamed: getRandomRockTextureBySize(2))
            medRockGroup.append(ff)//   insert(ff)
        }
//        
//        //MSall Rocks
        for _ in 1...maxSmlRocksForPool {
            let ff = Rock(theSize:3, mass: CGFloat.random(in:0.1...0.19), imageNamed: getRandomRockTextureBySize(3))
            smlRockGroup.append(ff)//   insert(ff)
        }
        
    }
    
    func getRandomRockTextureBySize(_ theSize:Int) -> String{
        
        var str:String?
        
        let s = Int.random(in: 1...3)
        str = "rock\(theSize)\(s).png"
        
        return str!
    }
    
    
    func launchRock(_ theSize:Int, x:CGFloat, y:CGFloat){
        
        
        switch theSize{
        case GameData.kROCK_BIG:
            
            bigRockGroup[currentBigRock].launch(x,y: y)
            
            currentBigRock += 1
            if (currentBigRock >= bigRockGroup.count-1){
                currentBigRock = 0
            }
            
            break
        case GameData.kROCK_MED:
            medRockGroup[currentMedRock].launch(x,y: y)
            
            currentMedRock += 1
            if (currentMedRock >= medRockGroup.count-1){
                currentMedRock = 0
            }
            
            break
        case GameData.kROCK_SML:
            smlRockGroup[currentSmlRock].launch(x,y: y)
            
            currentSmlRock += 1
            if (currentSmlRock >= smlRockGroup.count-1){
                currentSmlRock = 0
            }
            
            break
            
            
        default:
            break
            
        }
        
    }
    
    func update(){
        let startTimeInterval1 = CFAbsoluteTimeGetCurrent()
        wrapBigRocks()
        wrapMedRocks()
        wrapSmlRocks()
//        print("Time1: \(CFAbsoluteTimeGetCurrent() - startTimeInterval1)") // "Time1: 0.489435970783234"
        
    }
    
    func wrapAllRocks(){
        // Takes about 0.0002549886703491211
//        let startTimeInterval1 = CFAbsoluteTimeGetCurrent()

        bigRockGroup.forEach {
            if ($0.alive){
                $0.screenWrap(width: $0.parent!.frame.size.width ,height: $0.parent!.frame.size.height)
            }
        }
        medRockGroup.forEach {
            if ($0.alive){
                $0.screenWrap(width: $0.parent!.frame.size.width,height: $0.parent!.frame.size.height)
            }
        }
        smlRockGroup.forEach {
            if ($0.alive){
                $0.screenWrap(width: $0.parent!.frame.size.width,height: $0.parent!.frame.size.height)
            }
        }
//        print("Time1: \(CFAbsoluteTimeGetCurrent() - startTimeInterval1)") // "Time1: 0.489435970783234"

    }
    
    func wrapBigRocks(){
        bigRockGroup.forEach {
            if ($0.alive){
                $0.screenWrap(width: GameData.stageWidth!,height: GameData.stageHeight!)
            }
        }
    }
    func wrapMedRocks(){
        for r in medRockGroup{
            if r.alive{
                r.screenWrap(width: GameData.stageWidth!,height: GameData.stageHeight!)
            }
        }
    }
    func wrapSmlRocks(){
        // animate the bullets
        
        
        for r in smlRockGroup{
            if r.alive{
                r.screenWrap(width: GameData.stageWidth!,height: GameData.stageHeight!)
            }
        }
        
    }
}
