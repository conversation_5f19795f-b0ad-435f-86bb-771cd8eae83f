//
//  GameViewController.swift
//  Debris Field macOS
//
//  Created by <PERSON> on 2/26/21.
//

import Cocoa
import SpriteKit
import GameKit


//var skView:SKView?

class GameViewController: GCEventViewController,GK<PERSON>iewController, GKGameCenterControllerDelegate {
    


    override func viewDidLoad() {
        super.viewDidLoad()
        
        
        let scene = SKScene(fileNamed: "LogoBumperScene")
        SceneManager.shared.currentScene = scene
        // Present the scene
        SceneManager.shared.mainSKView = self.view as? SKView
        SceneManager.shared.mainSKView?.presentScene(scene)
        SceneManager.shared.mainSKView?.ignoresSiblingOrder = false
        
        
#if DEBUG
//        SceneManager.shared.mainSKView?.showsPhysics = true
//        SceneManager.shared.mainSKView?.showsFPS = true
//        SceneManager.shared.mainSKView?.showsNodeCount = true
#endif
        
        //let it = NSApp.keyWindow
//        let it2 = NSApplication.shared.windows[0]
//
//        if !SceneManager.shared.mainSKView!.isInFullScreenMode {
//            //if let mainWindow = SceneManager.shared.mainSKView!.window {
//                it2.toggleFullScreen(nil)
//            //}
//        }
        
        
        // Start Game Center Here
        GameCenter.shared.authenticateLocalPlayer(presentingVC: self)
        
    }

    override func viewDidAppear() {
        //added in hopes that mouse moved events would be captured
        
        // Needed for move mouseEvents
        #if os(OSX)
            SceneManager.shared.mainSKView?.window?.acceptsMouseMovedEvents = true
            SceneManager.shared.mainSKView?.window?.becomeFirstResponder()
        #endif

    }
    func gameCenterViewControllerDidFinish(_ gameCenterViewController: GKGameCenterViewController) {}
    
}


// maybe all I need to do is start typing and looking at the code.  This would be a better test.
