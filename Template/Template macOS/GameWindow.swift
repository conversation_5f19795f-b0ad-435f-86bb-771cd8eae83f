//
//  GameWindow.swift
//  Template
//
//  Created by <PERSON> on 4/21/21.
//

import Cocoa

class GameWindowController:NSWindowController, NSWindowDelegate{
    
//    required init?(coder aDecoder: NSCoder){
//        super.init(coder: aDecoder)
//    }
//
    override func windowDidLoad() {
        super.windowDidLoad()
        //window?.delegate = self
        
        self.window?.styleMask.insert(NSWindow.StyleMask.unifiedTitleAndToolbar)
        self.window?.styleMask.insert(NSWindow.StyleMask.fullSizeContentView)
//        self.window?.styleMask.insert(NSWindow.StyleMask.titled)
        self.window?.toolbar?.isVisible = false
        self.window?.titleVisibility = .hidden
        self.window?.center()
        self.window?.titlebarAppearsTransparent = false
        self.window?.isMovable = true
        
    }
    
    func windowWillResize(_ sender: NSWindow, to frameSize: NSSize) -> NSSize {
        sender.aspectRatio = NSSize(width: 16.0, height: 9.0)
        return frameSize
    }

   
}
