//
//  AppDelegate.swift
//  Debris Field macOS
//
//  Created by <PERSON> on 2/26/21.
//

import Cocoa
import Foundation
//import SwiftyStoreKit

@main
class AppDelegate: NSObject, NSApplicationDelegate {



    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // Insert code here to initialize your application
        //Analytics.shared.startAnalytics("Mac")
//        SwiftyStoreKit.completeTransactions(atomically: true) { purchases in
//                for purchase in purchases {
//                    switch purchase.transaction.transactionState {
//                    case .purchased, .restored:
//                        if purchase.needsFinishTransaction {
//                            // Deliver content from server, then:
//                            SwiftyStoreKit.finishTransaction(purchase.transaction)
//                        }
//                        // Unlock content
//                    case .failed, .purchasing, .deferred:
//                        break // do nothing
//                    @unknown default:
//                        print("unknown storekit error")
//                    }
//                }
//            }
        
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        // Insert code here to tear down your application
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> <PERSON><PERSON> {
        return true
    }


}

