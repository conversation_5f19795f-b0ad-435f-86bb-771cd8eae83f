//
//  GameViewController.swift
//  Debris Field tvOS
//
//  Created by <PERSON> on 2/26/21.
//

import UIKit
import SpriteKit
import GameplayKit
import GameKit

var skView:SKView?


class GameViewController: GCEventViewController{ //UIViewController {
    
    @IBOutlet weak var markKigi: UIImageView!
    @IBOutlet var MarkLogo: UIImageView!
    override func viewDidLoad() {
        super.viewDidLoad()
        
        let scene = SKScene(fileNamed: "MenuScene")

        SceneManager.shared.vc = self;
        SceneManager.shared.mainSKView = self.view as? SKView
        SceneManager.shared.mainSKView?.presentScene(scene)
        SceneManager.shared.mainSKView?.ignoresSiblingOrder = false
#if DEBUG
        SceneManager.shared.mainSKView?.showsFPS = true
        SceneManager.shared.mainSKView?.showsNodeCount = true
//        SceneManager.shared.mainSKView?.showsPhysics = false
#endif
        
        GameCenter.shared.authenticateLocalPlayer(presentingVC: self)
    }

}
