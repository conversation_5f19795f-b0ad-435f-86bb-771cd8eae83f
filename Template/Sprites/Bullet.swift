//
//  Bullet.swift
//  De<PERSON>s Field
//
//  Created by <PERSON> on 3/30/21.
//

import Foundation
import SpriteKit

final class Bullet:SKSpriteNode{
    
    
    var accelerationLength:CGFloat = 0.99;
    var dragRate:CGFloat = 0.99;
    var speed_limit:CGFloat  = 15.0;
    var useLUT = false;
    var angle:CGFloat = 0.0
    
    var offScreenPosition:CGPoint = CGPoint(x: -20, y: -20)
    
    //var body:SKPhysicsBody?
    
    var lifeSpan:Int?
    var alive:Bool = false
    var mySpeed:CGFloat = 17.5
    var mySpeed2:CGFloat = 57.5
    var velocity:CGPoint = CGPoint(x: 0.0,y: 0.0)
    var _scale:CGPoint = CGPoint(x: 0.0,y: 0.0)
    
	#if os(iOS) || os(tvOS)
	
	override init(texture: SKTexture?, color: UIColor, size: CGSize) {
		super.init(texture: texture, color: color, size: texture!.size())
		
		// Do custom stuff here
		alive = false
		lifeSpan = 0
		applyPhysics()
		position = offScreenPosition
	}
	#endif
	
	#if os(OSX)
    override init(texture: SKTexture?, color: NSColor, size: CGSize) {
        super.init(texture: texture, color: color, size: texture!.size())
        
        // Do custom stuff here
        alive = false
        lifeSpan = 0
        applyPhysics()
        position = offScreenPosition
    }
	#endif
	
    func applyPhysics(){
        //        body = SKPhysicsBody(circleOfRadius: self.size.width/2)
        //        body?.mass = 1.0
        self.physicsBody = SKPhysicsBody(circleOfRadius: (self.texture?.size().height)!/2.0)
        self.physicsBody!.isDynamic = true
        self.physicsBody!.affectedByGravity = false
        self.physicsBody!.allowsRotation = false
        self.physicsBody!.mass = 0.07
        self.physicsBody!.linearDamping = 0.0; //0.3; //DRAG & speed limit
        self.physicsBody!.angularDamping = 0.0 //default = 0.11
        self.physicsBody!.friction = 0.0
        self.physicsBody!.restitution = 0.0 //  default = 0.2 //bouncyness
        
        self.physicsBody!.categoryBitMask = PhysicsCollisionMasks.bullet
        self.physicsBody!.collisionBitMask = 0x00000000 //GameDataClass.BodyCategory.nothing.rawValue
        self.physicsBody!.contactTestBitMask = PhysicsCollisionMasks.rock
        
        //1 << GameDataClass.BodyType.bullet.rawValue  //BodyType.rock.rawValue //was toRaw() in Xcode 6
        //if you don't want this body to actually collide with anything
        // was toRaw() in Xcode 6
        
        
        // sensorBody.categoryBitMask    = GameDataClass.BodyCategory.rock.rawValue; // Set sensor category bit
        // sensorBody.collisionBitMask   = 0x00000000;      // Prevent all collisions
        // sensorBody.contactTestBitMask = 0x00000000;
        
        
        /*
         body.mass = myMass
         body.friction = 0.0
         body.restitution = 1.0 //  default = 0.2 //bouncyness
         body.linearDamping = 0.0 //default = 0.1
         body.angularDamping = 0.0 //default = 0.1
         */
        //body?.usesPreciseCollisionDetection = true
        
        
        
    }
    /* convenience init(imageNamed: String!){
     let color = UIColor()
     let texture = SKTexture(imageNamed: imageNamed)
     let size =  texture.size() // CGSizeMake(100.0, 100.0)
     self.init(texture: texture, color: color, size: size)
     
     alive = false
     lifeSpan = 0
     }*/
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func shoot2(_ ltype:Int, vx:CGFloat, vy:CGFloat,angle:CGFloat,span:Int){
        
        GameData.stage!.addChild(self)
        
        lifeSpan = span
        alive = true
        
    
        _scale.x = cos(angle) * mySpeed  //CGFloat(GameData.lookUpTable.__sine2[Int(angle)] * CGFloat(10.0))
        _scale.y = sin(angle) * mySpeed  //GameData.lookUpTable.__cosine2[Int(angle)] * CGFloat(10.0))
        
        
        self.position = (GameData.hero?.position)!
        
        velocity.x = vx //+ vector.getVector().x
        velocity.y = vy //+ vector.getVector().y
        
    }
    
    func shoot(_ ltype:Int, vx:CGFloat, vy:CGFloat,angle:CGFloat,span:Int){
        
        GameData.stage!.addChild(self)
        
        lifeSpan = span
        alive = true
    
        
        // set and calculate the vector of the bullet
        self.position = GameData.hero.position
        let dx =  cos(angle) * self.mySpeed2 ;
        let dy =  sin(angle) * self.mySpeed2 ;
        
        // Apply the veloicy of the ship to the bullet
        self.physicsBody?.velocity = GameData.hero.physicsBody!.velocity;
        // Add the speed of the bullet on top of it's veloicty
        self.physicsBody?.applyImpulse(CGVector(dx: dx,  dy: dy))
        
        
    }
    
    func update() {
        lifeSpan? -= 1
        if (lifeSpan! >= 0){
            // used without physics
            //position.x = position.x + _scale.x + velocity.x;
            //position.y = position.y + _scale.y + velocity.y;
            screenWrap(width: parent!.frame.size.width,height: parent!.frame.size.height);
            
            if (lifeSpan! == 0){
                kill()
            }
        }
    }
    
    func kill(){
        alive = false
        //        position = offScreenPosition
        velocity.x = 0
        velocity.y = 0
        
        //self.physicsBody = nil
        self.removeFromParent();
    }
    
    
    func screenWrap() {
        if (alive){
            if (self.position.x < 0) {
                self.position.x = GameData.stageWidth!;
            }
            else if (self.position.x > GameData.stageWidth!) {
                self.position.x = 0;
            }
            if (self.position.y < 0) {
                self.position.y = GameData.stageHeight!;
            }
            else if (self.position.y > GameData.stageHeight!) {
                self.position.y = 0;
            }
        }
    }
    
}//end class

