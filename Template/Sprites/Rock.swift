//
//  Rock.swift
//  Aerolites
//
//  Created by <PERSON> on 11/20/15.
//  Copyright © 2015 <PERSON>. All rights reserved.
//

import Foundation
import SpriteKit

class Rock: SKSpriteNode {
    
    
    //    override var position : CGPoint
    //    {
    //        didSet
    //        {
    //            if (self.position.x < 0.0) {
    //                self.position = CGPoint(x: GameData.stageWidth!, y: self.position.y)
    //            } else if (self.position.x > GameData.stageWidth!) {
    //                self.position = CGPoint(x: 0.0, y: self.position.y)
    //            }
    //            if (super.position.y < 0) {
    //                self.position = CGPoint(x: self.position.x, y: GameData.stageHeight!)
    //            }else if (self.position.y > GameData.stageHeight!) {
    //                self.position = CGPoint(x: self.position.x, y: 0.0)
    //            }
    //        }
    //    }
    //
    var alive = false
    var myMass:CGFloat = 0.5
    //    var body:SKPhysicsBody?
    //var offScreenPosition:CGPoint  = CGPoint(x: -200,y: -200)
    
    var mySize:Int?
    //    var x:CGFloat{
    //        didSet {
    //            //physicsBody!.linearDamping = drag
    //            //screenWrap(width: GameData.stageWidth!,height: GameData.stageHeight!);
    //        }
    //    }
    
    
    
    /*required init?(coder aDecoder: NSCoder) {
     super.init(coder: aDecoder)
     self.anchorPoint = CGPointMake(0.5, 0.5)
     velocity = CGPointMake(0.0, 0.0)
     
     }
     */
	
	#if os(iOS) || os(tvOS)
	override init(texture: SKTexture!, color: UIColor, size: CGSize) {
		super.init(texture: texture, color: color, size: texture!.size())
		// Do custom stuff here
		applyPhysics()
		// position = offScreenPosition
		alive = false
		isHidden = true
	}
	#endif
	#if os(OSX)
    override init(texture: SKTexture!, color: NSColor, size: CGSize) {
        super.init(texture: texture, color: color, size: texture!.size())
        // Do custom stuff here
        applyPhysics()
        // position = offScreenPosition
        alive = false
        isHidden = true
		
    }
	#endif
    func applyPhysics(){
       
		// This is where the context leak is created!  Not sure why
       self.physicsBody = SKPhysicsBody(texture: self.texture!, alphaThreshold: 0.0, size: self.texture!.size() )
		//self.physicsBody = SKPhysicsBody(circleOfRadius: 20.0) // NO CONTEXT LEAK! <- for reference

        //        self.physicsBody!.isDynamic = true
        self.physicsBody?.affectedByGravity = false
        self.physicsBody?.allowsRotation = true
        
        
        self.physicsBody?.categoryBitMask = PhysicsCollisionMasks.rock  //BodyType.rock.rawValue //was toRaw() in Xcode 6
        self.physicsBody?.collisionBitMask = PhysicsCollisionMasks.nothing //if you don't want this body to actually collide with anything
        self.physicsBody?.contactTestBitMask = PhysicsCollisionMasks.hero | PhysicsCollisionMasks.bullet
        //
        self.physicsBody?.mass = myMass
        self.physicsBody?.friction = 0.0
        self.physicsBody?.restitution = 0.0 //  default = 0.2 //bouncyness
        self.physicsBody?.linearDamping = 0.0 //default = 0.1
        self.physicsBody?.angularDamping = 0.0 //default = 0.1
        //self.physicsBody?.usesPreciseCollisionDetection = true
        
        
        //        self.physicsBody? = body
    }
    
	#if os(OSX)
	convenience init(theSize:Int, mass:CGFloat,imageNamed: String!){
		let color = NSColor()
		let texture = SKTexture(imageNamed: imageNamed)
		let size =  texture.size() // CGSizeMake(100.0, 100.0)
		self.init(texture: texture, color: color, size: size)
		
		// init shit after here
		myMass = mass
		mySize = theSize
	}
	#endif
	
	#if os(iOS) || os(tvOS)
    convenience init(theSize:Int, mass:CGFloat,imageNamed: String!){
        let color = UIColor()
        let texture = SKTexture(imageNamed: imageNamed)
        let size =  texture.size() // CGSizeMake(100.0, 100.0)
        self.init(texture: texture, color: color, size: size)
        
        
        // init shit after here
        
        myMass = mass
        mySize = theSize
        
        
    }
	#endif
	
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func launch(_ x:CGFloat, y:CGFloat){
        
        
        // must set position first!!! 
        // otherwise rock will start at top of screen!? for some reason
        position.x = x
        position.y = y
        
        //self.physicsBody = body
        
        // This might be a bottle neck.  I'm sure it is
        GameData.stage?.addChild(self)
		
		self.position = CGPoint(x: 0, y: 0)
		
//		let sizea = vector_float2(Float(self.size.width), Float(self.size.height))
//		self.setValue(SKAttributeValue(vectorFloat2: sizea), forAttribute: "a_size")
//		self.shader = createPixelate()
		
        self.physicsBody?.mass = myMass
        let a = CGFloat.random(in:0...360)
        let n = CGFloat.random(in:25...100)
        let s = sin(a) * n
        let c = cos(a) * n
        let v = CGVector(dx: s, dy: c)
        physicsBody?.applyImpulse(v)
        physicsBody?.applyAngularImpulse(CGFloat.random(in:0.05...0.1) * CGFloat.random(in:-0.3...0.3)  )
        
        //self.position.x = CGFloat.random(0, upper: GameData.stageWidth!)
        //self.position.y = -10
        
        
        // !!!!! SETTING THE ANCHOR FUCKS UP THE COLLISION BOUNDRIES!!!! FUCK FUCK FUCK FUCK!!!!
        //if (mySize < 3){
        //  self.anchorPoint = CGPoint(x: CGFloat.random(0.15, upper: 0.85),y: CGFloat.random(0.15, upper: 0.85))
        // }
        
        alive = true
        isHidden = false
        GameData.totalRocksOnScreen = GameData.totalRocksOnScreen + 1
        
		
        
    }
    
    
    
    func hit(){
        
        alive = false
        self.physicsBody?.velocity.dx = 0.0
        self.physicsBody?.velocity.dy = 0.0;
        //  return
        
        
        //var newSize:Int?
        // var scoreAmount:Int?
        // var data:NSDictionary?
        
        var howManyRocks = 1
        
        if ( Int.chanceRoll(50) ){
            howManyRocks += 1
        }
        
        if ( Int.chanceRoll(25) ){
            howManyRocks += 1
        }
        
        let rx = position.x
        let ry = position.y
        
        switch mySize{
        case GameData.kROCK_BIG?:
            // newSize = 2
            // scoreAmount = 1
            
            NotificationCenter.default.post(name: Notification.Name(rawValue: "launchAnotherRock"), object: nil,userInfo: ["rockCount":howManyRocks,"size" : 2, "score" : 1, "x" : rx , "y" : ry])
            
            //             NotificationCenter.default.post(name: Notification.Name(rawValue: "shake"), object: nil,userInfo: ["x" : self.position.x , "y" : self.position.y])
            
            
            
            break
        case GameData.kROCK_MED?:
            // scoreAmount = 2
            // newSize = 1
            NotificationCenter.default.post(name: Notification.Name(rawValue: "launchAnotherRock"), object: nil,userInfo: ["rockCount":howManyRocks,"size" : 3, "score" : 2, "x" : rx , "y" : ry])
            
            break
        case GameData.kROCK_SML?:
            //scoreAmount = 3
            //NotificationCenter.default.post(name: Notification.Name(rawValue: "updateScore"), object: 3)
            
            //            NotificationCenter.default.post(name: Notification.Name(rawValue: "launchAnotherRock"), object: nil,userInfo: ["rockCount":1,"size" : GameData.kNewRockAndOrBonus, "score" : 0, "x" : rx , "y" : ry])
            
            
            // launch bonus item??? here??
            
            break
        default:
            break
        }
        
        // NSNotificationCenter.defaultCenter().postNotificationName("updateScore", object: scoreAmount)
        // NSNotificationCenter.defaultCenter().postNotificationName("launchAnotherRock", object: nil, userInfo: )
        
        kill()
    }
    
    
    
    func kill(){
        alive = false
        isHidden = true
        //        position = offScreenPosition
        self.physicsBody?.velocity.dx = 0.0
        self.physicsBody?.velocity.dy = 0.0;
        //self.physicsBody = nil
        
        GameData.totalRocksOnScreen -= 1
        
        if (GameData.totalRocksOnScreen < 0){
            GameData.totalRocksOnScreen = 0
        }
        
        self.removeFromParent()
    }
    
    //    func screenWrap() {
    //        if (alive){
    //            if (self.position.x < 0) {
    //                self.position.x = GameData.stageWidth!;
    //            }
    //            else if (self.position.x > GameData.stageWidth!) {
    //                self.position.x = 0;
    //            }
    //            if (self.position.y < 0) {
    //                self.position.y = GameData.stageHeight!;
    //            }
    //            else if (self.position.y > GameData.stageHeight!) {
    //                self.position.y = 0;
    //            }
    //        }
    //    }
    
    //    func update(_ currentTime: TimeInterval) {
    //
    //        // used without physics
    //        //position.x = position.x + _scale.x + velocity.x;
    //        //position.y = position.y + _scale.y + velocity.y;
    //        screenWrap(width: GameData.stageWidth!,height: GameData.stageHeight!);
    //
    //
    //    }
    
    
}
