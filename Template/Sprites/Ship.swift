//
//  Ship.swift
//  De<PERSON><PERSON> Field
//
//  Created by <PERSON> on 2/26/21.
//  Copyright © 2015 <PERSON>. All rights reserved.
//
//farts

import Foundation
import SpriteKit
//import UIKit



class Ship: SKNode{ //SKSpriteNode {
    
    var drag:CGFloat = 0.0 {
        didSet {
//            physicsBody!.linearDamping = drag
        }
    }
            
    
    // put in update
    //        let cx = position.x
    //        let cy = position.y
    //        position = CGPoint(x: cx, y: cy)
    //override var position : CGPoint
    //    {
    //        didSet
    //        {
    //            if (self.position.x < 0.0) {
    //                self.position = CGPoint(x: GameData.stageWidth!, y: self.position.y)
    //            } else if (self.position.x > GameData.stageWidth!) {
    //                self.position = CGPoint(x: 0.0, y: self.position.y)
    //            }
    //            if (super.position.y < 0) {
    //                self.position = CGPoint(x: self.position.x, y: GameData.stageHeight!)
    //            }else if (self.position.y > GameData.stageHeight!) {
    //                self.position = CGPoint(x: self.position.x, y: 0.0)
    //            }
    //        }
    //    }

    
    var accelerationLength_phys:CGFloat = 2.5; //0.99

    
    var flame:SKSpriteNode?
    var ship:SKSpriteNode?
    var shield:SKSpriteNode?
    
    var shieldsOn:Bool = false
    var shieldTimer:Int = 0
    
            
    // TRAILS
    var launchTrailTime:CGFloat = 4.0
    var currentTrailTime:CGFloat = 0.0
    //    var shipTrailsGroup: [ShipTrail] = []
    var currentTrailID = 0
    
    
    var energy:CGFloat = 100.0
    
    
    override init(){
        super.init()
        // }
        // override init(texture: SKTexture!, color: UIColor, size: CGSize) {
        //  super.init(texture: texture, color: color, size: size)
        // Do custom stuff here
        
       
        GameData.stage!.addChild(self)
        self.position = CGPoint(x: GameData.stageWidth!/2, y: GameData.stageHeight!/2)

        
        
        
        flame =  SKSpriteNode(imageNamed: "flame.png") // self.childNodeWithName("flame") as? SKSpriteNode
        flame?.isHidden = true
        flame?.position.x -= 20
        flame?.name = "flame"
        
        self.addChild(flame!)
        
        ship = SKSpriteNode(imageNamed: "ship2")
        
        self.addChild(ship!)
        
        shield = SKSpriteNode(imageNamed: "shield")
        self.addChild(shield!)
        shield?.isHidden = true
        
        applyPhysics()
        
        
        
        //        for _ in 1...50 {
        //            let ff = ShipTrail(imageNamed: "flame")
        //            //ff.zPosition = self.zPosition-1
        //
        //            shipTrailsGroup.append(ff)//   insert(ff)
        //        }
        
		self.zPosition = GameData.zorder.ship.rawValue
        
        /* fragShader.uniforms = [
         SKUniform(name: "iChannel0", texture: SKTexture(imageNamed: imageNamed))
         ]*/
        //ship!.shader = GameData.fragShader
        
        // ship!.shader = fragShader
        // set position to middle of the screen
       
        
        reset()
        
    }
    
    /* convenience init(imageNamed: String){
     let color = UIColor()
     let texture = SKTexture(imageNamed: imageNamed)
     let size =  texture.size() // CGSizeMake(100.0, 100.0)
     self.init(texture: texture, color: color, size: size)
     
     
     //applyPhysics()
     
     }*/
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    func reset(){
        drag = 0.0
        energy = 100
       
        shield?.isHidden = true
        flame?.isHidden = true
    }
    
    func applyPhysics(){
        //  body  = SKPhysicsBody(texture: self.texture!, size: self.size)
        
//        self.physicsBody = SKPhysicsBody(texture: ship!.texture!, alphaThreshold: 0.0, size: ship!.texture!.size() )
//        ship!.physicsBody?.affectedByGravity = false
//		self.physicsBody?.categoryBitMask = PhysicsCollisionMasks.hero  //BodyType.rock.rawValue //was toRaw() in Xcode 6
//		self.physicsBody?.collisionBitMask = PhysicsCollisionMasks.nothing //if you don't want this body to actually collide with anything
//		self.physicsBody?.contactTestBitMask =  PhysicsCollisionMasks.bonus | PhysicsCollisionMasks.rock
//        
        
    }
    
    func initThisShit(){
       
    }
    
   
    
    func thrust() {
        let dx = cos(self.zRotation) * self.accelerationLength_phys;
        let dy = sin(self.zRotation) * self.accelerationLength_phys;
        self.physicsBody!.applyImpulse(CGVector(dx: dx, dy: dy))
        flame?.isHidden = false
        flame?.zRotation = CGFloat.random(in:1...360).degreesToRadians
    }//end thrust
    
    func stopTrust(){
        flame?.isHidden = true;
    }
    
    func rotate(_ dir:CGFloat) {
        self.zRotation -= GameData.rotSpeed * dir * 12.0
    }
    
    
    func update() {
        
    
        //
        // Im not sire this is the fastest was.  It's always calculating as opposed to if statement
        //screenWrap(width:GameData.stageWidth!,height:GameData.stageHeight!);

        
        if (position.x < 0) {
            position.x = parent!.frame.size.width;
        } else if (position.x > parent!.frame.size.width) {
            position.x = 0;
        }
        if (position.y < 0) {
            position.y = parent!.frame.size.height;
        }else if (position.y > parent!.frame.size.height) {
            position.y = 0;
        }
        
        
        //
        //        if (shieldsOn){
        //            shieldTimer -= 1
        //            if (shieldTimer <= 0){
        //                shieldsOn = false
        //                shield?.isHidden = true
        //            }
        //        }
        //
        
        
        //        for b in shipTrailsGroup{
        //            if b.alive{
        //                b.update()
        //            }
        //        }
        
    }
    
    //    func watchTrail(){
    //        currentTrailTime -= 1
    //        if (currentTrailTime <= 0){
    //            currentTrailTime = launchTrailTime
    //
    //            shipTrailsGroup[currentTrailID].launch()
    //
    //            currentTrailID += 1
    //            if (currentTrailID >= shipTrailsGroup.count){
    //                currentTrailID = 0
    //            }
    //        }
    //    }
    
    
    func hit(_ howMuch:Int){
        
        var e:CGFloat = 0.0;
        
        switch howMuch{
        case 1:
            e = 0.25
            break
        case 2:
            e = 0.15
            break
        case 3:
            e = 0.15
            break
        default:
            e = 0.0
            break
        }
        
        energy = energy - e
        
        if (energy < 0.0 ){
            explodeAndDie()
        }
        
    }
    
    func explodeAndDie(){
        // create pieces sprites and scatter them like bullet bonus
        // spin them and fade them
        // shake screen hard
        // shockwave it too?
        
    }
    
    func kill(){
        if (shieldsOn == false){
            
        }
    }
    
    //    func screenWrap() {
    //
    //        self.position.x = self.position.x.wrap(min: 0-GameData.stageWidth!/2, max: GameData.stageWidth!/2)
    //        self.position.y = self.position.y.wrap(min: 0-GameData.stageHeight!/2, max: GameData.stageHeight!/2)
    //
    //    }
    
    
    func startShield(){
        if (shieldsOn == true){
            shieldTimer += 250
        }else{
            shieldTimer = 500
            shieldsOn = true
            shield?.isHidden = false
        }
    }
    
}

