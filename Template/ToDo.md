- Create better sfx for clicks and nav
- check UImage overlay performance
- make UI switch only visible on move buttons scene
- preload audio and play simple sound at begining to prevent first time button latency (make a logo sound)

- add haptics on/off toggle in prefs
- add official gamecenter logo

❌ add haptics to buttons
❌ better integrate StoreKit2
❌ change generic virtual button icons to icons that show what action is being taken. (Per Apple guidelines)
