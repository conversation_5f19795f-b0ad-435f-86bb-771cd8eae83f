<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BV1-FR-VrT">
    <device id="retina6_12" orientation="landscape" appearance="light"/>
    <dependencies>
        <deployment version="5888" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Game View Controller-->
        <scene sceneID="tXr-a1-R10">
            <objects>
                <viewController id="BV1-FR-VrT" customClass="GameViewController" customModule="Template" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" multipleTouchEnabled="YES" contentMode="scaleToFill" id="3se-qz-xqx" customClass="SKView">
                        <rect key="frame" x="0.0" y="0.0" width="852" height="393"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <switch hidden="YES" opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" on="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3YY-ew-gyQ">
                                <rect key="frame" x="402" y="181" width="51" height="31"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            </switch>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" image="mark-logo.png" translatesAutoresizingMaskIntoConstraints="NO" id="I9w-XM-gjx">
                                <rect key="frame" x="389" y="350" width="78" height="28"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="78" id="7HP-5i-SqI"/>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="28" id="bz5-66-GVN"/>
                                    <constraint firstAttribute="width" constant="78" id="h49-RM-bxs"/>
                                    <constraint firstAttribute="height" constant="28" id="rRY-bE-DAa"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="bNo-s9-wk6"/>
                        <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="I9w-XM-gjx" firstAttribute="leading" secondItem="3se-qz-xqx" secondAttribute="leading" constant="389" id="Hz1-pY-Nay"/>
                            <constraint firstAttribute="bottomMargin" secondItem="I9w-XM-gjx" secondAttribute="bottom" constant="-40" id="VRa-VF-gie"/>
                            <constraint firstAttribute="bottom" secondItem="I9w-XM-gjx" secondAttribute="bottom" constant="15" id="eXy-V8-XaA"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="markLogo" destination="I9w-XM-gjx" id="cK4-U5-sRr"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="SZV-WD-TEh" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="139.43661971830986" y="2.2900763358778624"/>
        </scene>
    </scenes>
    <resources>
        <image name="mark-logo.png" width="393" height="246"/>
    </resources>
</document>
