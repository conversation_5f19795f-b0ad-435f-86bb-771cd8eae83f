//
//  HapticManager.swift
//  Template
//
//  Created by <PERSON> on 8/19/25.
//


// MARK: - Haptic Feedback Manager

#if os(iOS)
import UIKit
import AudioToolbox

/// Manager class for handling haptic feedback on iOS devices
class HapticManager {

    static let shared = HapticManager()

    // MARK: - Feedback Generators
    private let lightImpactGenerator = UIImpactFeedbackGenerator(style: .light)
    private let mediumImpactGenerator = UIImpactFeedbackGenerator(style: .medium)
    private let heavyImpactGenerator = UIImpactFeedbackGenerator(style: .heavy)
    private let selectionGenerator = UISelectionFeedbackGenerator()
    private let notificationGenerator = UINotificationFeedbackGenerator()

    // MARK: - Haptic Types
    enum HapticType {
        case buttonPress        // Light impact for button presses
        case buttonRelease      // Selection feedback for button releases
        case menuNavigation     // Medium impact for menu navigation
        case gameAction         // Heavy impact for game actions
        case success            // Success notification
        case warning            // Warning notification
        case error              // Error notification
    }

    private init() {
        // Prepare generators for better performance
        prepareGenerators()
    }

    // MARK: - Public Methods

    /// Trigger haptic feedback if haptics are enabled in user preferences
    /// - Parameter type: The type of haptic feedback to trigger
    func triggerHaptic(_ type: HapticType) {
        // Only trigger haptics on iOS and if user has haptics enabled
        guard UserPrefs.hapticsOn else { return }

        switch type {
        case .buttonPress:
            //print("🔥 Triggering light impact")
            lightImpactGenerator.impactOccurred()

        case .buttonRelease:
            //print("🔥 Triggering selection feedback")
            selectionGenerator.selectionChanged()

        case .menuNavigation:
            //print("🔥 Triggering medium impact")
            mediumImpactGenerator.impactOccurred()

        case .gameAction:
            //print("🔥 Triggering heavy impact")
            heavyImpactGenerator.impactOccurred()

        case .success:
            //print("🔥 Triggering success notification")
            notificationGenerator.notificationOccurred(.success)

        case .warning:
            //print("🔥 Triggering warning notification")
            notificationGenerator.notificationOccurred(.warning)

        case .error:
            //print("🔥 Triggering error notification")
            notificationGenerator.notificationOccurred(.error)
        }
    }

    /// Prepare all feedback generators for optimal performance
    /// Call this when you know haptic feedback will be used soon
    func prepareGenerators() {
        guard UserPrefs.hapticsOn else { return }

        lightImpactGenerator.prepare()
        mediumImpactGenerator.prepare()
        heavyImpactGenerator.prepare()
        selectionGenerator.prepare()
        notificationGenerator.prepare()
    }

    /// Convenience method for button press haptic
    func buttonPressed() {
        triggerHaptic(.buttonPress)
    }

    /// Convenience method for button release haptic
    func buttonReleased() {
        triggerHaptic(.buttonRelease)
    }

    /// Convenience method for menu navigation haptic
    func menuNavigated() {
        triggerHaptic(.menuNavigation)
    }

    /// Convenience method for game action haptic
    func gameAction() {
        triggerHaptic(.gameAction)
    }

    /// Test function to verify haptics are working
    func testHaptics() {
        //print("🔥 Testing haptics - UserPrefs.hapticsOn: \(UserPrefs.hapticsOn)")

        // Test if device supports haptics
        if #available(iOS 10.0, *) {
            //print("🔥 Device supports haptics (iOS 10+)")

            // Force trigger haptic regardless of user preference for testing
            let testGenerator = UIImpactFeedbackGenerator(style: .heavy)
            testGenerator.prepare()
            testGenerator.impactOccurred()
            //print("🔥 Heavy haptic triggered directly")

            // Also test through our system
            triggerHaptic(.gameAction)
            //print("🔥 Game action haptic triggered through HapticManager")

            // Try legacy haptic method as fallback
            AudioServicesPlaySystemSound(kSystemSoundID_Vibrate)
            //print("🔥 Legacy vibration triggered")
        } else {
            //print("❌ Device does not support haptics (iOS < 10)")
            // Try legacy method for older devices
            AudioServicesPlaySystemSound(kSystemSoundID_Vibrate)
            //print("🔥 Legacy vibration triggered for older device")
        }
    }
}

#else

/// Stub implementation for non-iOS platforms
class HapticManager {
    static let shared = HapticManager()

    enum HapticType {
        case buttonPress, buttonRelease, menuNavigation, gameAction, success, warning, error
    }

    private init() {}

    func triggerHaptic(_ type: HapticType) {
        // No-op on non-iOS platforms
    }

    func prepareGenerators() {
        // No-op on non-iOS platforms
    }

    func buttonPressed() {}
    func buttonReleased() {}
    func menuNavigated() {}
    func gameAction() {}
}

#endif
