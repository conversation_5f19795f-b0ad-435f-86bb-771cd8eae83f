//
//  GameViewController.swift
//  Debris Field iOS
//
//  Created by <PERSON> on 2/26/21.
//

import UIKit
import SpriteKit
import GameplayKit
import GameKit
//import GoogleMobileAds

var skView:SKView?

class GameViewController: GCEventViewController {

    @IBOutlet var markLogo: UIImageView!
    
    var localPlayer = GKLocalPlayer.local
    
    override func viewDidLoad() {
        super.viewDidLoad()
       
        
        let scene = SKScene(fileNamed: "LogoBumperScene")
        // Present the scene
        SceneManager.shared.currentScene = scene
        SceneManager.shared.vc = self;
        SceneManager.shared.mainSKView = self.view as? SKView
        SceneManager.shared.mainSKView?.presentScene(scene)
        SceneManager.shared.mainSKView?.ignoresSiblingOrder = true
#if DEBUG
//        SceneManager.shared.mainSKView?.showsFPS = true
//        SceneManager.shared.mainSKView?.showsNodeCount = true
//        SceneManager.shared.mainSKView?.showsPhysics = false
#endif
        
        GameCenter.shared.authenticateLocalPlayer(presentingVC: self)
        
        markLogo.isHidden = UserPrefs.mainPurchase
        
        if (UserPrefs.mainPurchase == false){
           
//            loadBanner()
//            loadFullscreenAd()
        }
        
       
    }
    
    func hideLogo(it:Bool){
        self.markLogo.isHidden = it
    }
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        setNeedsUpdateOfHomeIndicatorAutoHidden()
    }
    
    override var shouldAutorotate: Bool {
        return true
    }
    override var preferredScreenEdgesDeferringSystemGestures: UIRectEdge {
        return UIRectEdge.bottom
    }
    
    @available(iOS 11, *)
    override var childForHomeIndicatorAutoHidden: UIViewController? {
        return nil
    }
    
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        if UIDevice.current.userInterfaceIdiom == .phone {
            return .landscape
        } else {
            return .all
        }
    }

    override func setNeedsUpdateOfHomeIndicatorAutoHidden() {}
    
    override var prefersStatusBarHidden: Bool {
        return true
    }
    override var prefersHomeIndicatorAutoHidden: Bool {
        return true
    }
    
}


extension UIView {
    
    func subviewsRecursive() -> [UIView] {
        return subviews + subviews.flatMap { $0.subviewsRecursive() }
    }
    
}
