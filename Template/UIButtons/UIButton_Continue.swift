//
//  UIButton.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit

class UIButton_Continue:MenuButtonNode, UIButtonDelegate{
	
//	var active = false;
	
	required init?(coder aDecoder: NSCoder) {
		super.init(coder: aDecoder)
        isHidden = true
	}
	
	override func onButtonDown(){
        let it = self.scene as! GameScene
        it.buttonPressed("Button A", button: self)
	}
	
	override func onButtonUp(){
//        GameData.playSound(GameData.sound_buttonSelect)
        isHidden = true
    }
	
	override func onDeactivate() {
		
		self.active = false
	}
	
	override func onActivate() {
		if (active){
			return
		}
		active = true
	}
	
}
