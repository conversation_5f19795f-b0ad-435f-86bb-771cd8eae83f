//
//  UIButton_Pause.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

#if os(OSX)
    import Cocoa
#endif

import Foundation
import SpriteKit
import GameKit

class UIButton_GameCenter:MenuButtonNode, UIButtonDelegate  {
    
    var observation:NSKeyValueObservation?
    
    
    
    override func onButtonDown(){
        GameData.playSound(GameData.sound_buttonSelect)
        if (GameCenter.shared.isGameCenterEnabled == true) {
            GKAccessPoint.shared.trigger {}
        }
        
        
        //win?.windowLevel = win!.windowLevel - 2.0
        //GameCenter.shared.highlightView.removeFromSuperview()
        
//        self.run(SKAction.wait(forDuration: 0.75)) {
//            let fs = self.scene as! FoundationScene
//            fs.resetIndex()
//        }
        
        
    }

    
    override func onButtonUp(){
        
    }
    
    override func onDeactivate() {
        active = false
        resetcolor()
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        colorme()
        
    }
    
//    override func onDeactivate() {
//        active = false
//        //GameCenter.shared.highlightView.removeFromSuperview()
//
//    }
//    override func onActivate(){
//
//        if (active){
//            return
//        }
//        active = true
    
        
//        let screenFrame = GKAccessPoint.shared.frameInScreenCoordinates
//        let accessFrame = SceneManager.shared.mainSKView?.convert(screenFrame, from: nil)
//
        
//        position = accessFrame!.origin
        
//        #if os(OSX)
//
//            GameCenter.shared.highlightView.frame = GKAccessPoint.shared.frameInScreenCoordinates
//            GameCenter.shared.highlightView.layer?.cornerRadius = GKAccessPoint.shared.frameInScreenCoordinates.width/2
//            GameCenter.shared.getGCWindow()?.contentView?.addSubview(GameCenter.shared.highlightView)
////            GameCenter.shared.getGCWindow()?.contentView?.subviews[0].addSubview(v)
//
//
////            GameCenter.shared.getGCWindow()?.addChildWindow(win!, ordered: .above)
////
////            guard let w = GameCenter.shared.getGCWindow() else {
////                return
////            }
////            win?.setFrame(w.frame, display: true)
////
////            v.setFrameSize(NSSize(width: GKAccessPoint.shared.frameInScreenCoordinates.width,
////                                  height: GKAccessPoint.shared.frameInScreenCoordinates.height))
////
////            v.setFrameOrigin(NSPoint(x: GKAccessPoint.shared.frameInScreenCoordinates.origin.x, y: GKAccessPoint.shared.frameInScreenCoordinates.origin.y))
////
////            v.layer?.cornerRadius = GKAccessPoint.shared.frameInScreenCoordinates.width/2
////
////            win?.contentView?.addSubview(v)
//
//        #else
//
//
//            GameCenter.shared.highlightView.frame = GKAccessPoint.shared.frameInScreenCoordinates
//            GameCenter.shared.highlightView.layer.cornerRadius = GKAccessPoint.shared.frameInScreenCoordinates.width/2
//            GameCenter.shared.getGCWindow()?.subviews[0].addSubview(GameCenter.shared.highlightView)
//// //            GameCenter.shared.getGCWindow()?.subviews[0].sendSubviewToBack(v)
//
//
//        #endif
        
    //}
    
    
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    
    
        //isHidden = true
        
//        #if os(OSX)
//
//            // Make a new window
////            win = NSWindow(contentRect: GKAccessPoint.shared.frameInScreenCoordinates, styleMask: .borderless, backing: .buffered, defer: true)
////            win?.backgroundColor = .clear
////            win?.isOpaque = false
////
////
////            win?.ignoresMouseEvents = true
//
//
//
//            GameCenter.shared.highlightView.wantsLayer = true
//            GameCenter.shared.highlightView.layer?.borderWidth = 3.0
//            GameCenter.shared.highlightView.layer?.borderColor =  NSColor.systemOrange.cgColor
//        #else
//            GameCenter.shared.highlightView.layer.borderWidth = 3.0
//            GameCenter.shared.highlightView.layer.borderColor = UIColor.orange.cgColor
//            GameCenter.shared.highlightView.isUserInteractionEnabled = false
//        #endif
    
        observation = GKAccessPoint.shared.observe(\.isPresentingGameCenter) { [weak self] _,_ in
            if GKAccessPoint.shared.isPresentingGameCenter == true {
                // self?.onDeactivate()
                //GameCenter.shared.highlightView.removeFromSuperview()
            }else{
                //self?.onActivate()
                
            }
        }
        
        
    }
    
}

