//
//  UIButton.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit

class UIButton_NavigateToScene:MenuButtonNode, UIButtonDelegate{
    
    var theSceneNavigateTo:String?
    
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        self.zPosition = 9999;
        if let targetScene = userData?["sceneToNavTo"] as? String {
            theSceneNavigateTo = targetScene
        }
    }
    
    override func onDeactivate() {
        active = false
        resetcolor()
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        colorme()
        
    }
    override func onButtonUp(){
        // Just to the main scene for now
      //  super.onButtonUp()
        if (theSceneNavigateTo == "GameScene"){
            GameData.playSound(GameData.sound_buttonPlay)
            HapticManager.shared.gameAction()
        }else if (theSceneNavigateTo == "MenuScene"){
            GameData.playSound(GameData.sound_buttonBack)
            HapticManager.shared.menuNavigated()
        }else{
            GameData.playSound(GameData.sound_buttonSelect)
            HapticManager.shared.menuNavigated()
        }
        SceneManager.shared.gotoScene(theSceneNavigateTo ?? "MenuScene")
    }
    
    override func onButtonDown(){
    }
    
   
    #if os(OSX)
//    @objc override func onMouseEnter(){
//        colorme()
//
//    }
//    @objc override func onMouseExit() {
//        resetcolor()
//    }
    #endif
    
    
}
