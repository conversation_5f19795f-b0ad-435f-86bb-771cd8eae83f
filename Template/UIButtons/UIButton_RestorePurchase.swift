//
//  UIButton_Pause.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

#if os(OSX)
	import Cocoa
#endif

import Foundation
import SpriteKit
import GameKit
import StoreKitTheKit

class UIButton_RestorePurchase:MenuButtonNode, UIButtonDelegate  {
	
    var clicked = false;
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    
    
        //isHidden = true
        
//        #if os(OSX)

//        #else

//        #endif
    
        
        
    }
    
	override func onButtonDown(){

        if (clicked == true){
            return
        }

        clicked = true
        self.alpha = 0.25

        // Perform restore asynchronously
        Task {
            let result = await PurchaseManager.shared.restorePurchases()

            await MainActor.run {
                self.handleRestoreResult(result)
            }
        }
	}

	
	override func onButtonUp(){}
	
    override func onDeactivate() {
        active = false
        resetcolor()
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        colorme()

    }

    // MARK: - Restore Result Handling

    private func handleRestoreResult(_ result: RestoreResult) {
        // Reset button state
        clicked = false
        alpha = 1.0

        switch result {
        case .success(let restoredCount):
            if restoredCount > 0 {
                print("Restore Successful: \(restoredCount) purchase(s) restored")

                // Find and hide the purchase button since the purchase is now restored
                if let scene = SceneManager.shared.currentScene,
                   let purchaseButton = scene.childNode(withName: "//UIButton_Purchase") as? UIButton_Purchase {
                    purchaseButton.isHidden = true
                }

                #if os(iOS)
                // TODO: Implement removeAds() method when ready
                // SceneManager.shared.vc?.removeAds()
                #endif

                GameData.showAlert("Success! 😀", theMessage: "Your purchase was restored") {
                    SceneManager.shared.gotoScene("MenuScene")
                }
            } else {
                print("Nothing to Restore")
                GameData.showAlert("No Purchases Found", theMessage: "There are no purchases to restore.") {
                    SceneManager.shared.gotoScene("MenuScene")
                }
            }

        case .failure(let error):
            print("Restore Failed: \(error)")
            GameData.showAlert("Restore Failed", theMessage: error.localizedDescription) {
                SceneManager.shared.gotoScene("MenuScene")
            }
        }
    }

}

