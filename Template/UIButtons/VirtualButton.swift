//
//  UIButton.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit
#if os(iOS)
import UIKit
#endif

class VirtualButton: MenuButtonNode, UIButtonDelegate {
    
    // MARK: - Constants
    static let defaultButtonSize: CGFloat = 100.0
    static let minButtonSize: CGFloat = 10.0
    static let resizeHandleSize: CGFloat = 80.0
    static let resizeHandleOffset: CGFloat = 20.0
    static let minScale: CGFloat = 0.05
    static let scaleSensitivity: CGFloat = 0.1
    static let scaleSpeedDown: CGFloat = 0.9
    static let scaleSpeedUp: CGFloat = 1.1
    static let handleHitAreaPadding: CGFloat = 20.0

    // MARK: - Resize Constants
    private let MIN_SCALE: CGFloat = 0.5
    private let MAX_SCALE: CGFloat = 2.0
    private let RESIZE_HANDLE_SIZE: CGFloat = 85.0
    private let fixedDistanceFromEdge: CGFloat = 20.0
    
    // MARK: - Resize Properties
    private var resizeHandle: SKShapeNode?
    private var debugFrame: SKShapeNode?
    private var isResizing = false
    private var initialTouchLocation: CGPoint = .zero
    private var initialScale: CGFloat = 1.0
    
    // MARK: - Reset positions
    private var orgX: Int?
    private var orgY: Int?
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        #if !os(iOS)
        self.isHidden = true
        #else
        self.zPosition = GameData.zorder.vButtons.rawValue
        isVirtualButton = true
        
        if let button = userData?["buttonName"] as? String {
            theButton = button
            orgX = userData?["orgX"] as? Int
            orgY = userData?["orgY"] as? Int
        } else {
            assertionFailure("\n---- \(self.name ?? "UIBUTTON") Button name not set in userData! ---- \n")
            theButton = "Button A"
        }
        
        // Restore saved position
        if let pos = UserDefaults.standard.string(forKey: "\(name!)_position") {
            let np = NSCoder.cgPoint(for: pos)
            position = np
        }
        
        // Restore saved scale
        let savedScale = UserDefaults.standard.float(forKey: "\(name!)_scale")
        if savedScale > 0 {
            setScale(CGFloat(savedScale))
        }
        
        // Setup resize handle
        setupResizeHandle()
        
        // Visibility rules
        if GameState.currentState == .settings {
            self.isHidden = false
        } else if UserPrefs.hideControls {
            self.isHidden = true
        }
        #endif
    }
    
    #if os(iOS)
    func resetToOriginalPosition() {
        let bounceAnimator = BounceAnimationNode()
        addChild(bounceAnimator)
        resizeHandle?.isHidden = true
        
        bounceAnimator.animateSprite(self, to: CGPoint(x: CGFloat(orgX!), y: CGFloat(orgY!)), scale: 1.0, duration: 0.25, bounceIntensity: 0.0) {
            self.updateResizeHandlePosition()
            self.resizeHandle?.isHidden = false
            
            UserDefaults.standard.setValue(Float(self.xScale), forKey: "\(self.name!)_scale")
            let pos = NSCoder.string(for: CGPoint(x: CGFloat(self.orgX!), y: CGFloat(self.orgY!)))
            UserDefaults.standard.setValue(pos, forKey: "\(self.name!)_position")
        }
    }
    
    // MARK: - Resize Handle Setup
    private func setupResizeHandle() {
        let path = CGMutablePath()
        let radius = RESIZE_HANDLE_SIZE / 1.2
        
        // Half-circle arc (bottom-right quadrant)
        path.addArc(center: .zero,
                    radius: radius,
                    startAngle: 0,
                    endAngle: -.pi / 2,
                    clockwise: true)
        
        resizeHandle = SKShapeNode(path: path)
        resizeHandle?.fillColor = .clear
        resizeHandle?.strokeColor = UIColor.white.withAlphaComponent(0.8)
        resizeHandle?.lineWidth = 21.0
        resizeHandle?.lineCap = .round
        resizeHandle?.zPosition = 1
        
        updateResizeHandlePosition()
        
        resizeHandle?.isHidden = GameState.currentState != .settings
        
        if let handle = resizeHandle {
            addChild(handle)
        }
    }
    
    /// Positions the resize handle at a fixed distance from the bottom-right corner.
    private func updateResizeHandlePosition() {
        guard let handle = resizeHandle else { return }
        
        let buttonSize: CGSize
        if let texture = self.texture {
            buttonSize = texture.size()
        } else {
            buttonSize = self.size
        }
        
        let unscaledX = (buttonSize.width / 2) - Self.resizeHandleOffset
        let unscaledY = -(buttonSize.height / 2) + Self.resizeHandleOffset
        
        handle.position = CGPoint(
            x: unscaledX / self.xScale,
            y: unscaledY / self.yScale
        )
        
        handle.setScale(1.0 / self.xScale)
    }
    private func isTouchOnResizeHandle(_ location: CGPoint) -> Bool {
        guard let handle = resizeHandle, !handle.isHidden else { return false }
        
        let handleFrame = handle.calculateAccumulatedFrame()
        let expandedFrame = handleFrame.insetBy(dx: -Self.handleHitAreaPadding, dy: -Self.handleHitAreaPadding)
        
        return expandedFrame.contains(location)
    }
    
    override func setScale(_ scale: CGFloat) {
        super.setScale(scale)
        updateResizeHandlePosition()
    }
    
    override func onButtonDown() {}
    
    override func onButtonUp() {
        if GameState.currentState == .playing ||
            GameState.currentState == .eolCountComplete ||
            GameState.currentState == .levelWait {
            let it = self.scene as! GameScene
            it.buttonReleased(theButton!, button: self)
        }
    }
    
    func onButtonDown(_ touches: Set<UITouch>, with event: UIEvent?) {
        if GameState.currentState == .settings {
            if let touch = touches.first {
                let location = touch.location(in: self)
                
                if isTouchOnResizeHandle(location) {
                    isResizing = true
                    initialScale = xScale
                    initialTouchLocation = touch.location(in: scene!)
                } else {
                    let sceneLocation = touch.location(in: scene!)
                    offset = CGPoint(x: self.position.x - sceneLocation.x, y: self.position.y - sceneLocation.y)
                }
            }
        } else if GameState.currentState == .playing ||
                    GameState.currentState == .eolCountComplete ||
                    GameState.currentState == .levelWait {
            let it = self.scene as! GameScene
            it.buttonPressed(theButton!, button: self)
        }
    }
    
    func onButtonMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        if GameState.currentState == .settings {
            if let touch = touches.first {
                if isResizing {
                    let currentTouchLocation = touch.location(in: scene!)
                    let buttonCenter = position
                    
                    let initialDistance = hypot(initialTouchLocation.x - buttonCenter.x, initialTouchLocation.y - buttonCenter.y)
                    let currentDistance = hypot(currentTouchLocation.x - buttonCenter.x, currentTouchLocation.y - buttonCenter.y)
                    
                    if initialDistance > 0 {
                        let distanceRatio = currentDistance / initialDistance
                        let newScale = initialScale * distanceRatio
                        let clampedScale = max(MIN_SCALE, min(MAX_SCALE, newScale))
                        setScale(clampedScale)
                        
                        // Update handle after scaling
                        updateResizeHandlePosition()
                    }
                } else {
                    let location = touch.location(in: scene!)
                    position = CGPoint(x: location.x + offset!.x, y: location.y + offset!.y)
                }
            }
        }
    }
    
    func onButtonUp(_ touches: Set<UITouch>, with event: UIEvent?) {
        if GameState.currentState == .settings {
            if isResizing {
                UserDefaults.standard.setValue(Float(xScale), forKey: "\(name!)_scale")
                isResizing = false
            } else {
                let pos = NSCoder.string(for: position)
                UserDefaults.standard.setValue(pos, forKey: "\(name!)_position")
            }
        }
    }
    
    override var isHidden: Bool {
        didSet {
            if GameState.currentState == .settings {
                resizeHandle?.isHidden = false
            } else {
                resizeHandle?.isHidden = true
            }
        }
    }
    
    #endif
}
