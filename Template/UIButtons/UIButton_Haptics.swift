//
//  UIButton_Haptics.swift
//  Template
//
//  Created by Augment Agent on 8/19/25.
//

import Foundation
import SpriteKit

class UIButton_Haptics:MenuButtonNode, UIButtonDelegate{

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)

        #if !os(iOS)
        // Hide haptics button on non-iOS platforms since haptics are iOS-only
        self.isHidden = true
        #else
        let state = UserDefaults.standard.bool(forKey: "haptics")
        setButtonTexture(state)
        #endif
    }
    
    override func onButtonDown(){
        #if os(iOS)
        // Play sound feedback
        GameData.playSound(GameData.sound_buttonSelect)
        
        // Toggle haptics and trigger a haptic to demonstrate the new setting
        let state = UserPrefs.shared.toggleHaptics()
        setButtonTexture(state)
        
        // Give immediate haptic feedback to show the new setting
        if state {
            HapticManager.shared.triggerHaptic(.success)
        }
        #endif
    }
    
    override func onButtonUp(){}
    
    func setButtonTexture(_ state:Bool){
        #if os(iOS)
        // Check if this should be a circle button (following MenuButtonNode pattern)
        let useCircle = userData?["useCircle"] as? Bool ?? false

        if useCircle {
            // Hide the original texture and setup circle + symbol
            texture = nil

            // Get circle properties from userData (with defaults)
            let circleColorR = userData?["circleColorR"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_R
            let circleColorG = userData?["circleColorG"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_G
            let circleColorB = userData?["circleColorB"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_B
            let circleColorA = userData?["circleColorA"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_A

            let circleColor = UIColor(red: circleColorR, green: circleColorG, blue: circleColorB, alpha: circleColorA)

            // Setup circle if not already created
            if circleShape == nil {
                let radius = min(frame.width, frame.height) / 2
                setupCircle(radius: radius, color: circleColor)
            }

            // Setup SF Symbol
            setupHapticSFSymbol(state: state)
        } else {
            // Regular texture-based button with SF Symbol
            setupHapticSFSymbol(state: state)
        }
        #endif
    }

    private func setupCircle(radius: CGFloat, color: SKColor) {
        #if os(iOS)
        circleShape?.removeFromParent()

        circleShape = SKShapeNode(circleOfRadius: radius)
        circleShape?.fillColor = color
        circleShape?.strokeColor = .clear
        circleShape?.position = CGPoint.zero
        circleShape?.zPosition = MenuButtonNode.CIRCLE_Z_POSITION

        if let circle = circleShape {
            addChild(circle)
        }
        #endif
    }

    private func setupHapticSFSymbol(state: Bool) {
        #if os(iOS)
        // Remove existing symbol sprite
        symbolSprite?.removeFromParent()

        // Use SF Symbols for haptic button
        let symbolName = state ? "apple.haptics.and.music.note" : "apple.haptics.and.music.note.slash"

        // Get the effective symbol color
        let symbolColor = getEffectiveSymbolColor()

        // Get symbol size from userData or use default
        let symbolSize = userData?["symbolSize"] as? CGFloat ?? (frame.width * MenuButtonNode.DEFAULT_SYMBOL_SIZE_RATIO)
        let configuration = UIImage.SymbolConfiguration(pointSize: symbolSize, weight: .thin)

        if let symbolImage = UIImage(systemName: symbolName, withConfiguration: configuration) {
            let uiColor = symbolColor as UIColor

            let renderer = UIGraphicsImageRenderer(size: symbolImage.size)
            let coloredImage = renderer.image { context in
                uiColor.setFill()
                symbolImage.withTintColor(uiColor, renderingMode: .alwaysTemplate).draw(at: .zero)
            }

            let texture = SKTexture(image: coloredImage)
            symbolSprite = SKSpriteNode(texture: texture)
            symbolSprite?.position = CGPoint.zero
            symbolSprite?.zPosition = MenuButtonNode.SYMBOL_Z_POSITION

            if let sprite = symbolSprite {
                addChild(sprite)
            }
        } else {
            // Fallback to generic SF symbols if the haptic-specific ones aren't available
            let fallbackSymbol = state ? "speaker.wave.3" : "speaker.slash"
            if let fallbackImage = UIImage(systemName: fallbackSymbol, withConfiguration: configuration) {
                let uiColor = symbolColor as UIColor

                let renderer = UIGraphicsImageRenderer(size: fallbackImage.size)
                let coloredImage = renderer.image { context in
                    uiColor.setFill()
                    fallbackImage.withTintColor(uiColor, renderingMode: .alwaysTemplate).draw(at: .zero)
                }

                let texture = SKTexture(image: coloredImage)
                symbolSprite = SKSpriteNode(texture: texture)
                symbolSprite?.position = CGPoint.zero
                symbolSprite?.zPosition = MenuButtonNode.SYMBOL_Z_POSITION

                if let sprite = symbolSprite {
                    addChild(sprite)
                }
            }
        }
        #endif
    }

    private func getEffectiveSymbolColor() -> SKColor {
        #if os(iOS)
        // Use the same color logic as MenuButtonNode
        if colorBlendFactor > 0 {
            // Use the sprite's tint color with the sprite's alpha
            var r: CGFloat = 0, g: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
            color.getRed(&r, green: &g, blue: &b, alpha: &a)
            return UIColor(red: r, green: g, blue: b, alpha: alpha)
        } else {
            // No tint color, use default white with sprite's alpha
            return UIColor(red: MenuButtonNode.DEFAULT_SYMBOL_COLOR_R,
                          green: MenuButtonNode.DEFAULT_SYMBOL_COLOR_G,
                          blue: MenuButtonNode.DEFAULT_SYMBOL_COLOR_B,
                          alpha: alpha)
        }
        #else
        return SKColor.white
        #endif
    }
    
    override func onDeactivate() {
        self.active = false
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        let it = SKAction.scale(to: 1.25, duration: 0.15)
        #if os(OSX)
        let clr = SKAction.colorize(with: NSColor.systemOrange, colorBlendFactor: 1.0, duration: 0.15)
        
        #else
        let clr = SKAction.colorize(with: UIColor(red: 248.0, green: 185.0, blue: 0.0, alpha: 1.0),colorBlendFactor: 1.0, duration: 0.15)
        #endif
        
        let group = SKAction.group([it,clr])
        self.run(group)
        
    }
    
}
