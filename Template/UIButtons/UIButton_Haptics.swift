//
//  UIButton_Haptics.swift
//  Template
//
//  Created by Augment Agent on 8/19/25.
//

import Foundation
import SpriteKit

class UIButton_Haptics:MenuButtonNode, UIButtonDelegate{
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        #if !os(iOS)
        // Hide haptics button on non-iOS platforms since haptics are iOS-only
        self.isHidden = true
        #else
        let state = UserDefaults.standard.bool(forKey: "haptics")
        setButtonTexture(state)
        #endif
    }
    
    override func onButtonDown(){
        #if os(iOS)
        // Play sound feedback
        GameData.playSound(GameData.sound_buttonSelect)
        
        // Toggle haptics and trigger a haptic to demonstrate the new setting
        let state = UserPrefs.shared.toggleHaptics()
        setButtonTexture(state)
        
        // Give immediate haptic feedback to show the new setting
        if state {
            HapticManager.shared.triggerHaptic(.success)
        }
        #endif
    }
    
    override func onButtonUp(){}
    
    func setButtonTexture(_ state:Bool){
        #if os(iOS)
        var textureName = "hapticsOff"
        if state == true{
            textureName = "hapticsOn"
        }
        
        // Check if textures exist, fallback to sound textures if haptic textures don't exist
        if SKTexture(imageNamed: textureName).size() == CGSize(width: 32, height: 32) {
            // Texture doesn't exist (default 32x32 size), use sound button textures as fallback
            textureName = state ? "soundOn" : "soundOff"
        }
        
        texture = SKTexture(imageNamed: textureName)
        #endif
    }
    
    override func onDeactivate() {
        self.active = false
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        let it = SKAction.scale(to: 1.25, duration: 0.15)
        #if os(OSX)
        let clr = SKAction.colorize(with: NSColor.systemOrange, colorBlendFactor: 1.0, duration: 0.15)
        
        #else
        let clr = SKAction.colorize(with: UIColor(red: 248.0, green: 185.0, blue: 0.0, alpha: 1.0),colorBlendFactor: 1.0, duration: 0.15)
        #endif
        
        let group = SKAction.group([it,clr])
        self.run(group)
        
    }
    
}
