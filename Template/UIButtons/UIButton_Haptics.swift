//
//  UIButton.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit

class UIButton_Haptics:MenuButtonNode, UIButtonDelegate{
    
//    var active = false;
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
       
    }
    
    override func onButtonDown(){
        // TOGGLE MUSIC
        GameData.playSound(GameData.sound_buttonSelect)
        HapticManager.shared.menuNavigated()
       
    }
    
    override func onButtonUp(){}
    
    func setButtonTexture(_ state:Bool){
       
    }
    
    override func onDeactivate() {

        self.active = false
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
    }
    
}
