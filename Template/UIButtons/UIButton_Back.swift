//
//  UIButton.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit

class UIButton_Back:MenuButtonNode, UIButtonDelegate{
    
    var theSceneNavigateTo:String?
 
    
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        self.zPosition = 9999;
        if let targetScene = userData?["sceneToNavTo"] as? String {
            theSceneNavigateTo = targetScene
        }
    }
    
    override func onDeactivate() {
        active = false
        resetcolor()
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        colorme()
        
    }
    override func onButtonUp(){
        if ((isHidden) || (alpha == 0.0)){
            return
        }
            
        // Just to the main scene for now
//#if os(iOS)
//       SceneManager.shared.vc?.showFullscreenAd()
//#endif

       GameData.playSound(GameData.sound_buttonBack)
       
//        let it = self.scene as! GameScene
//        it.cleanUpAndKill()
        SceneManager.shared.currentScene?.removeAllChildren()
        SceneManager.shared.gotoScene(theSceneNavigateTo ?? "MenuScene")
        
        
    }
    
    override func onButtonDown(){
    }
    
   
    #if os(OSX)
//    @objc override func onMouseEnter(){
//        colorme()
//
//    }
//    @objc override func onMouseExit() {
//        resetcolor()
//    }
    #endif
    
    
}
