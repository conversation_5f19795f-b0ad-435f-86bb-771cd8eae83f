//
//  UIButton_Pause.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//


import Foundation
import SpriteKit

class UIButton_Pause:MenuButtonNode, UIButtonDelegate  {
    
    override func onButtonDown(){
        
        if (GameState.currentState == .initialLoad){
           return
        }
        
        let it = self.scene as! GameScene
        it.buttonReleased("Button Menu", button: self)
    }
    
    override func onButtonUp(){
        
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        #if os(tvOS) || os(macOS)
            self.isHidden = true
        #endif
    }
    
}
