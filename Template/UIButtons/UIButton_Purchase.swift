//
//  UIButton_Pause.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

#if os(OSX)
	import Cocoa
#endif

import Foundation
import SpriteKit
import GameKit
import StoreKitTheKit

class UIButton_Purchase:MenuButtonNode, UIButtonDelegate  {
	

    var clicked = false;
    
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    
        if (UserPrefs.mainPurchase == true){
           isHidden = true
        }
        //
        //ignore = true;
        
        #if !os(iOS)
            texture = SKTexture(imageNamed: "registerButton")
        #endif
    
        
        
    }
    
	override func onButtonDown(){

        if (clicked == true){
            return
        }

        clicked = true
        self.alpha = 0.25

        // Navigate to purchase wait scene first
        SceneManager.shared.gotoScene("PurchaseWaitScene")

        // Perform purchase asynchronously
        Task {
            let result = await PurchaseManager.shared.purchaseRemoveAds()

            await MainActor.run {
                self.handlePurchaseResult(result)
            }
        }
	}

	override func onButtonUp(){}
	
    override func onDeactivate() {
        active = false
        resetcolor()
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        colorme()

    }

    // MARK: - Purchase Result Handling

    private func handlePurchaseResult(_ result: PurchaseResult) {
        // Reset button state
        clicked = false
        alpha = 1.0

        switch result {
        case .success:
            print("Purchase Success: Remove Ads")

            // Hide the purchase button since the purchase is now complete
            isHidden = true

            #if os(iOS)
            // TODO: Implement removeAds() method when ready
            // SceneManager.shared.vc?.removeAds()
            #endif

            GameData.showAlert("Thank You! 😀", theMessage: "Your purchase was successful") {
                SceneManager.shared.gotoScene("MenuScene")
            }

        case .failure(let error):
            handlePurchaseError(error)
        }
    }

    private func handlePurchaseError(_ error: PurchaseError) {
        switch error {
        case .userCancelled:
            // User cancelled - just go back to menu without showing error
            SceneManager.shared.gotoScene("MenuScene")

        case .storeUnavailable, .networkError:
            showRetryableError(error)

        case .productNotFound:
            GameData.showAlert("Error", theMessage: "Product not available. Please try again later.") {
                SceneManager.shared.gotoScene("MenuScene")
            }

        case .purchasePending:
            GameData.showAlert("Purchase Pending", theMessage: "Your purchase is pending approval. You will receive the content once approved.") {
                SceneManager.shared.gotoScene("MenuScene")
            }

        case .unknown, .storeError:
            GameData.showAlert("Error", theMessage: error.localizedDescription) {
                SceneManager.shared.gotoScene("MenuScene")
            }
        }
    }

    private func showRetryableError(_ error: PurchaseError) {
        // For retryable errors, we could implement a retry mechanism
        // For now, just show the error and go back to menu
        GameData.showAlert("Error", theMessage: error.localizedDescription) {
            SceneManager.shared.gotoScene("MenuScene")
        }
    }

}

