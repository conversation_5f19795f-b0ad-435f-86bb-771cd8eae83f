//
//  UIButton_Pause.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

#if os(OSX)
	import Cocoa
#endif

import Foundation
import SpriteKit
import GameKit
//import SwiftyStoreKit

class UIButton_ResetVirtualBtnPositions:MenuButtonNode, UIButtonDelegate  {
	
    var clicked = false;
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    
    
        //isHidden = true
        
//        #if os(OSX)

//        #else

//        #endif
    
        
        
    }
    
	override func onButtonDown(){
       
//        if (clicked == true){
//            return
//        }
        
        //clicked = true
        //self.alpha = 0.25
        
//        resetToOriginalPosition()
        
        SceneManager.shared.currentScene?.enumerateChildNodes(withName: "//*") { node, _ in
            if let virtualButton = node as? VirtualButton {
                virtualButton.resetToOriginalPosition()
            }
        }
       
	}

	
	override func onButtonUp(){}
	
//    override func onDeactivate() {
//        active = false
//        resetcolor()
//    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        colorme()
        
    }
    
	
	
	
	
}

