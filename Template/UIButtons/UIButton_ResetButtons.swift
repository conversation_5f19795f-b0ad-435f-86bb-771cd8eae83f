//
//  UIButton.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation
import SpriteKit


class UIButton_ResetButtons:MenuButtonNode, UIButtonDelegate  {
    
//    var active = false;

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        let state = UserDefaults.standard.bool(forKey: "music")
        setButtonTexture(state)
    }
    
    override func onButtonDown(){
        // TOGGLE MUSIC
        GameData.playSound(GameData.sound_buttonSelect)
        let state = UserPrefs.shared.toggleMusic()
        setButtonTexture(state)
    }

    override func onButtonUp(){}
    
    func setButtonTexture(_ state:Bool){
        var textureName = "musicOff"
        if state == true{
            textureName = "musicOn"
        }
        texture = SKTexture(imageNamed: textureName)
    }

    override func onDeactivate() {
        let resetScale = SKAction.scale(to: 1.0, duration: 0.15)
        
        
        #if os(OSX)
        let clr = SKAction.colorize(with: NSColor(calibratedRed: 255.0, green: 255, blue: 255.0,                                         alpha: 1.0), colorBlendFactor: 1.0, duration: 0.15)
        #else
        let clr = SKAction.colorize(with: UIColor(red: 255.0, green: 255.0, blue: 255.0, alpha: 1.0),colorBlendFactor: 1.0, duration: 0.15)
        #endif
        let group = SKAction.group([resetScale,clr])
        self.run(group)
        self.active = false
    }
    
    override func onActivate() {
        if (active){
            return
        }
        active = true
        let it = SKAction.scale(to: 1.25, duration: 0.15)
        #if os(OSX)
        let clr = SKAction.colorize(with: NSColor.systemOrange, colorBlendFactor: 1.0, duration: 0.15)
        
        #else
        let clr = SKAction.colorize(with: UIColor(red: 248.0, green: 185.0, blue: 0.0, alpha: 1.0),colorBlendFactor: 1.0, duration: 0.15)
        #endif
        
        let group = SKAction.group([it,clr])
        self.run(group)
        
    }
}
