# Haptic Feedback Implementation

This document describes the haptic feedback system implemented for iOS devices in the Template project.

## Overview

Haptic feedback has been added to provide tactile responses to user interactions, enhancing the user experience on iOS devices. The system is designed to be:

- **iOS-only**: Haptics are only available on iOS devices
- **User-controllable**: Users can enable/disable haptics through preferences
- **Performance-optimized**: Feedback generators are prepared for optimal response times
- **Contextual**: Different haptic types for different interactions

## Components

### 1. HapticManager (in GameData.swift)
The central manager for all haptic feedback operations, integrated into the existing GameData.swift file.

**Key Features:**
- Singleton pattern for global access
- Multiple haptic types (light, medium, heavy, selection, notification)
- Automatic user preference checking
- Performance optimization through generator preparation

**Usage:**
```swift
// Basic usage
HapticManager.shared.buttonPressed()
HapticManager.shared.buttonReleased()
HapticManager.shared.menuNavigated()
HapticManager.shared.gameAction()

// Advanced usage
HapticManager.shared.triggerHaptic(.success)
HapticManager.shared.triggerHaptic(.warning)
HapticManager.shared.triggerHaptic(.error)
```

### 2. User Preferences
Added haptic preference to `UserPrefs` class:

- **Property**: `UserPrefs.hapticsOn` (Bool)
- **Default**: `true` (enabled by default)
- **Storage**: UserDefaults key "haptics"
- **Toggle method**: `UserPrefs.shared.toggleHaptics()`

### 3. UIButton_Haptics.swift
A toggle button for enabling/disabling haptics in the settings menu.

**Features:**
- Automatically hidden on non-iOS platforms
- Uses existing sound button textures as fallback
- Provides immediate haptic feedback when toggled
- Follows the same pattern as other UI buttons

## Haptic Types

| Type | Use Case | iOS Feedback |
|------|----------|--------------|
| `buttonPress` | Button press down | Light impact |
| `buttonRelease` | Button release | Selection feedback |
| `menuNavigation` | Menu transitions | Medium impact |
| `gameAction` | Game-specific actions | Heavy impact |
| `success` | Success notifications | Success notification |
| `warning` | Warning notifications | Warning notification |
| `error` | Error notifications | Error notification |

## Implementation Details

### Button Integration
Haptic feedback has been integrated into:

1. **MenuButtonNode**: Base button class with haptics on touch begin/end
2. **VirtualButton**: Game control buttons with contextual haptics
3. **UI Buttons**: Settings buttons with menu navigation haptics
4. **Navigation Buttons**: Scene transitions with appropriate haptics

### Performance Considerations
- Generators are prepared during initialization for optimal response times
- Haptic calls are lightweight and non-blocking
- User preference is checked before every haptic trigger
- No-op implementation for non-iOS platforms

### Platform Compatibility
- **iOS**: Full haptic feedback support
- **macOS/tvOS**: Stub implementation (no-op methods)
- **Conditional compilation**: Uses `#if os(iOS)` directives

## Adding Haptics to New Buttons

To add haptic feedback to a new button:

1. **Import the manager** (if not already available):
   ```swift
   // HapticManager is available globally
   ```

2. **Add haptic calls** in appropriate methods:
   ```swift
   override func onButtonDown() {
       HapticManager.shared.buttonPressed()
       // Your existing button logic
   }
   
   override func onButtonUp() {
       HapticManager.shared.buttonReleased()
       // Your existing button logic
   }
   ```

3. **Choose appropriate haptic type**:
   - Use `buttonPressed()/buttonReleased()` for standard buttons
   - Use `menuNavigated()` for navigation/settings buttons
   - Use `gameAction()` for game-specific actions
   - Use specific notification types for feedback messages

## Testing

To test haptic feedback:

1. **Enable haptics** in device settings (iOS Settings > Sounds & Haptics)
2. **Run on physical device** (haptics don't work in simulator)
3. **Toggle haptics** in app settings to test the preference system
4. **Test different button types** to experience various haptic intensities

## Notes

- Haptic feedback requires a physical iOS device (iPhone/iPad)
- The iOS Simulator does not support haptic feedback
- Users can disable haptics system-wide in iOS Settings
- The app respects both system-wide and app-specific haptic preferences
- Haptic feedback is automatically disabled on devices that don't support it

## Future Enhancements

Potential improvements for the haptic system:

1. **Custom haptic patterns** using Core Haptics framework
2. **Intensity settings** (light, medium, strong)
3. **Context-aware haptics** based on game state
4. **Accessibility integration** for enhanced user experience
5. **Haptic feedback for game events** (collisions, power-ups, etc.)
