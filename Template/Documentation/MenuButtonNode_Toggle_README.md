# MenuButtonNode Toggle Functionality

This document describes the toggle functionality added to the `MenuButtonNode` class, which allows buttons to maintain persistent on/off states with visual feedback.

## Overview

The toggle functionality is an **opt-in feature** that can be enabled on any `MenuButtonNode` through userData properties. When enabled, buttons can switch between ON and OFF states, with different visual appearances and persistent state storage.

## Key Features

- **Opt-in Design**: Only buttons with `useToggle: true` get toggle functionality
- **Persistent State**: Toggle states are saved to UserDefaults and restored on app launch
- **Visual Feedback**: Different colors and textures/symbols for ON/OFF states
- **Backward Compatible**: Non-toggle buttons work exactly as before
- **Circle Button Support**: Works with both regular and circle-style buttons
- **Flexible Assets**: Supports both texture-based and SF Symbol-based buttons

## userData Properties

### Required Properties

| Property | Type | Description |
|----------|------|-------------|
| `useToggle` | Bool | Set to `true` to enable toggle functionality |

### Optional Properties

| Property | Type | Description | Default |
|----------|------|-------------|---------|
| `toggleKey` | String | Custom UserDefaults key for state storage | Button's `name` property |
| `sfSymbol` | String | SF Symbol name for ON state | None |
| `sfSymbolOff` | String | SF Symbol name for OFF state | None |

## Visual States

### ON State (Default)
- **Color**: Uses original colors from Xcode's Attributes Inspector
- **Texture**: Uses original texture
- **SF Symbol**: Uses `sfSymbol` if no texture available
- **Circle**: Uses original circle color (if `useCircle: true`)

### OFF State
- **Color**: System red (`UIColor.systemRed`)
- **Texture**: Uses `originalTextureName + "_off"`
- **SF Symbol**: Uses `sfSymbolOff` if no texture available
- **Circle**: Red circle with symbol overlay

## Asset Priority

The button follows this priority order for visual assets:

1. **Texture** (highest priority)
   - ON state: Original texture
   - OFF state: `textureName + "_off"`

2. **SF Symbols** (fallback)
   - ON state: `userData["sfSymbol"]`
   - OFF state: `userData["sfSymbolOff"]`

## Usage Examples

### Basic Toggle Button with SF Symbols

```
useToggle: true (Bool)
sfSymbol: "speaker.wave.3" (String)
sfSymbolOff: "speaker.slash" (String)
```

### Toggle Button with Custom Storage Key

```
useToggle: true (Bool)
toggleKey: "soundEnabled" (String)
sfSymbol: "speaker.wave.3" (String)
sfSymbolOff: "speaker.slash" (String)
```

### Texture-Based Toggle Button

```
useToggle: true (Bool)
toggleKey: "musicEnabled" (String)
// Uses existing texture for ON state
// Automatically looks for "musicOn_off" texture for OFF state
```

### Circle Toggle Button

```
useToggle: true (Bool)
useCircle: true (Bool)
circleColorR: 0.2 (Number)
circleColorG: 0.8 (Number)
circleColorB: 0.2 (Number)
circleColorA: 0.8 (Number)
sfSymbol: "checkmark.circle" (String)
sfSymbolOff: "xmark.circle" (String)
```

### Haptics Toggle Button

```
useToggle: true (Bool)
toggleKey: "hapticsEnabled" (String)
useCircle: true (Bool)
circleColorR: 0.25 (Number)
circleColorG: 0.25 (Number)
circleColorB: 0.25 (Number)
circleColorA: 0.5 (Number)
sfSymbol: "apple.haptics.and.music.note" (String)
sfSymbolOff: "apple.haptics.and.music.note.slash" (String)
```

## Implementation Details

### State Management
- **Default State**: `true` (ON state)
- **Storage**: UserDefaults with key `toggleKey ?? buttonName`
- **Loading**: State restored during button initialization
- **Saving**: State saved immediately when toggled

### Toggle Trigger
- **Method**: `toggleState()` called on `onButtonDown()`
- **Scope**: Only affects buttons with `useToggle: true`
- **Thread Safety**: All operations on main thread

### Visual Updates
- **Immediate**: Visual changes apply instantly when toggled
- **Color Blending**: Uses existing color system for proper rendering
- **Z-Positioning**: Maintains proper layering (circle behind, symbol in front)

## Code Integration

### Accessing Toggle State

```swift
// Check if button has toggle functionality
if button.useToggle {
    // Get current state
    let isOn = button.isToggleOn
    
    // Manually toggle (if needed)
    button.toggleState()
}
```

### Custom Toggle Handling

```swift
// Override in subclass for custom toggle behavior
override func onButtonDown() {
    if useToggle {
        // Custom logic before toggle
        super.onButtonDown() // Calls toggleState()
        // Custom logic after toggle
    } else {
        // Non-toggle button behavior
        super.onButtonDown()
    }
}
```

## Debugging

### Console Output
Toggle operations produce debug output:
```
🔄 Toggle button setup - Key: soundEnabled, State: true
🔄 Button toggled - Key: soundEnabled, New State: false
```

### UserDefaults Inspection
Toggle states are stored in UserDefaults and can be inspected:
```swift
let isEnabled = UserDefaults.standard.bool(forKey: "soundEnabled")
```

## Best Practices

1. **Consistent Naming**: Use descriptive `toggleKey` values
2. **Asset Preparation**: Ensure `_off` textures exist for texture-based buttons
3. **SF Symbol Availability**: Test SF symbols on target iOS versions
4. **Color Contrast**: Ensure red OFF state provides good visual contrast
5. **User Feedback**: Consider adding haptic feedback for toggle actions

## Compatibility

- **iOS**: Full support including SF Symbols
- **macOS**: Full support with NSColor equivalents
- **Backward Compatibility**: Non-toggle buttons unaffected
- **Circle Buttons**: Full integration with existing circle button system

## Migration from Existing Toggle Buttons

For existing custom toggle buttons (like `UIButton_Music`), you can migrate to use the new system:

### Before (Custom Class):
```swift
class UIButton_Music: MenuButtonNode {
    override func onButtonDown() {
        let state = UserPrefs.shared.toggleMusic()
        setButtonTexture(state)
    }
}
```

### After (userData Configuration):
```
useToggle: true (Bool)
toggleKey: "music" (String)
sfSymbol: "music.note" (String)
sfSymbolOff: "music.note.slash" (String)
```

This provides the same functionality with less code and more flexibility.
