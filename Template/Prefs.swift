//
//  Defaults.swift
//  Template
//
//  Created by <PERSON> on 4/15/21.
//

import Foundation

struct UserPrefs{

    static let shared = UserPrefs()
    static var soundOn = false
    static var musicOn = false
    static var mainPurchase = false;
    static var hideControls = true;
    static var hapticsOn = true;
    
    public let userDefaults = UserDefaults.standard
    
    init() {
        var state:Bool = userDefaults.bool(forKey: "music")
        UserPrefs.musicOn = state
        state = userDefaults.bool(forKey: "soundfx")
        UserPrefs.soundOn = state

        state = userDefaults.bool(forKey: "hideControls")
        UserPrefs.hideControls = state

        // Default haptics to true if not set
        if userDefaults.object(forKey: "haptics") == nil {
            userDefaults.setValue(true, forKey: "haptics")
        }
        state = userDefaults.bool(forKey: "haptics")
        UserPrefs.hapticsOn = state

        state = userDefaults.bool(forKey: "mainPurchase")
        UserPrefs.mainPurchase = state

    }
    
    func initPrefs(){
        
       
    }
    
    func toggleMusic() -> Bool{
        var state:Bool = userDefaults.bool(forKey: "music")
        state = !state
        UserPrefs.musicOn = state
        userDefaults.setValue(state, forKey: "music")
        return state
    }
    
    func toggleSound() -> Bool{
        var state:Bool = userDefaults.bool(forKey: "soundfx")
        state = !state
        UserPrefs.soundOn = state
        userDefaults.setValue(state, forKey: "soundfx")
        return state
    }
    
    func toggleControls() -> Bool{
        var state:Bool = userDefaults.bool(forKey: "hideControls")
        state = !state
        UserPrefs.hideControls = state
        userDefaults.setValue(state, forKey: "hideControls")
        return state
    }

    func toggleHaptics() -> Bool{
        var state:Bool = userDefaults.bool(forKey: "haptics")
        state = !state
        UserPrefs.hapticsOn = state
        userDefaults.setValue(state, forKey: "haptics")
        return state
    }
    
    func tempFunctionToClearShit(){
        
    }
    
    func saveDatShit(_ state:Bool){
        UserPrefs.mainPurchase = true
        userDefaults.setValue(state, forKey: "mainPurchase")
    }
    
  
}
