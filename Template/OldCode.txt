


dict["Left Trigger"] = controllerItem.left_trigger
dict["Right Trigger"] = controllerItem.right_trigger
dict["Left Shoulder"] = controllerItem.left_shoulder
dict["Right Shoulder"] = controllerItem.right_shoulder
dict["Left Thumbstick Button"] = controllerItem.left_thumbStickButton
dict["Right Thumbstick Button"] = controllerItem.right_thumbStickButton
dict["Direction Pad Left"] = controllerItem.dpad_left
dict["Direction Pad Up"] = controllerItem.dpad_up
dict["Direction Pad Right"] = controllerItem.dpad_right
dict["Direction Pad Down"] = controllerItem.dpad_down
dict["Button Home"] = controllerItem.btn_home
dict["Button Options"] = controllerItem.btn_options
dict["Button Menu"] = controllerItem.btn_menu
dict["Button X"] = controllerItem.btn_x
dict["Button Y"] = controllerItem.btn_y
dict["Button B"] = controllerItem.btn_b
dict["Button A"] = controllerItem.btn_a

		 
["Left Trigger":left_trigger,
"Right Trigger":right_trigger,
"Left Shoulder":left_shoulder,
"Right Shoulder":right_shoulder,
"Left Thumbstick Button":left_thumbStickButton,
"Right Thumbstick Button":right_thumbStickButton,
"Direction Pad Left":dpad_left,
"Direction Pad Up":dpad_up,
"Direction Pad Right":dpad_right,
"Direction Pad Down":dpad_down,
"Button Home":btn_home,
"Button Options":btn_options,
"Button Menu":btn_menu,
"Button X":btn_x,
"Button Y":btn_y,
"Button B":btn_b,
"Button A":btn_a]


//			switch button {
//			case controller.extendedGamepad?.buttonA:
//				controlerFunc(controllerItem.btn_a)
//			case controller.extendedGamepad?.buttonB:
//				controlerFunc(controllerItem.btn_b)
//			case controller.extendedGamepad?.buttonX:
//				controlerFunc(controllerItem.btn_x)
//			case controller.extendedGamepad?.buttonY:
//				controlerFunc(controllerItem.btn_y)
//			// DPAD
//			case controller.extendedGamepad?.dpad.up:
//				controlerFunc(controllerItem.dpad_up)
//			case controller.extendedGamepad?.dpad.down:
//				controlerFunc(controllerItem.dpad_down)
//			case controller.extendedGamepad?.dpad.left:
//				controlerFunc(controllerItem.dpad_left)
//			case controller.extendedGamepad?.dpad.right:
//				controlerFunc(controllerItem.dpad_right)
//			// Extra buttons
//			case controller.extendedGamepad?.buttonMenu:
//				controlerFunc(controllerItem.btn_menu)
//			case controller.extendedGamepad?.buttonHome:
//				controlerFunc(controllerItem.btn_home)
//			case controller.extendedGamepad?.buttonOptions:
//				controlerFunc(controllerItem.btn_options)
//			//Thumbstick Buttons
//			case controller.extendedGamepad?.rightThumbstickButton:
//				controlerFunc(controllerItem.right_thumbStickButton)
//			case controller.extendedGamepad?.leftThumbstickButton:
//				controlerFunc(controllerItem.left_thumbStickButton)
//			//Shoulder Buttons
//			case controller.extendedGamepad?.rightShoulder:
//				controlerFunc(controllerItem.right_shoulder)
//			case controller.extendedGamepad?.leftShoulder:
//				controlerFunc(controllerItem.left_shoulder)
//			//Triggers
//			case controller.extendedGamepad?.rightTrigger:
//				controlerFunc(controllerItem.right_trigger)
//			case controller.extendedGamepad?.leftTrigger:
//				controlerFunc(controllerItem.left_trigger)
//
//			default:
//				break;
//			}


#if os(iOS)
		// add virtual controller buttons to scene as needed.  Keep them hidden
		
//		makeVirtualButton(withLabelSprite: "a.png", item: controllerItem.btn_a, x: 1630, y: 160)
//		makeVirtualButton(withLabelSprite: "b.png", item: controllerItem.dpad_up, x: 1890, y: 350)
//
//		makeVirtualButton(withLabelSprite: "x.png", item: controllerItem.dpad_right, x: 450, y: 160)
//		makeVirtualButton(withLabelSprite: "y.png", item: controllerItem.dpad_left, x: 190, y: 350)
//
		#endif
	
//	func makeVirtualButton(withLabelSprite name:String, item:controllerItem,x:CGFloat, y:CGFloat ){
//
//
//		let button = VirtualButton(imageNamed: "vButton.png");
//		let btnLabel = SKSpriteNode(imageNamed:name)
//		btnLabel.name = "label"
//		addChild(button)
//		button.addChild(btnLabel)
//		button.position.x = x
//		button.position.y = y
//
//		button.theVirtualButtonDelegate = self
//		button.buttonItem = item
//
//		virtualButtons?.append(button)
//
//	}
	


 // set Play to start!
		//		let it = SKSpriteNode(imageNamed: "ship2")
		//		self.addChild(it)
		//		it.position = CGPoint(x: frame.size.width/2, y: frame.size.height/2)
		//		let sizea = vector_float2(Float(it.size.width), Float(it.size.height))
		//
		//		it.setValue(SKAttributeValue(vectorFloat2: sizea), forAttribute: "a_size")
		//		it.shader = createPixelate()


	
//		let shader_move:SKShader = SKShader(fileNamed: "retro.fsh")
//		shader_move.addUniform(SKUniform(name: "iChannel0", texture: SKTexture(imageNamed: "dummy2")))
//		shader_move.addUniform(SKUniform(name: "iResolution", float: GLKVector2Make(2000.0,1500.0)))
//		shader = shader_move
//		shouldEnableEffects = true
//
		//		var it = GLKVector2Make(2000.0,1500.0)
		//
		//		shader_move.uniforms = [
		//			SKUniform(name: "iChannel0", texture: SKTexture(imageNamed: "dummy2")),
		//			SKUniform(name: "iResolution", vectorFloat2: it)
		//		]
		


GameData.theGamePad?.extendedGamepad?.buttonA.pressedChangedHandler = { (button,value,pressed) -> () in
			if (pressed){
				if (self.shoot == false){
					self.guns?.onButtonDown()
					self.shoot = true
				}
			}else{
				self.guns?.onButtonUp()
				self.shoot = false
			}
			
		}
