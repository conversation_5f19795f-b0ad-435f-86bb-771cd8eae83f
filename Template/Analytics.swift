//
//  Analytics.swift
//  De<PERSON>s Field
//
//  Created by <PERSON> on 4/14/21.
//

import Foundation


final class Analytics{
	
	static let shared = Analytics()
	
	#if DEBUG
		let googleID = "UA-194696859-1" //testing and debuging property
	#else
		let googleID = "UA-125441821-1" //production property
	#endif
	
	let url = URL(string: "https://www.google-analytics.com/collect?")!
	var gameName = Bundle.main.displayName
	let version = Bundle.main.version
	
	var cid_uid:String?
	var hardware:String?
	
	// I dont need specfic models.  I just want to see the analytics for each platform
	#if os(OSX)
		let device = "macOS"
	#elseif os(watchOS)
		let device = "watchOS"
	#elseif os(tvOS)
		let device = "tvOS"
	#elseif os(iOS)
		#if targetEnvironment(macCatalyst)
			let device = "macOS - Catalyst"
		#else
			var device = "iOS"
		#endif
	#endif
	
	
	//User Agent Override: ua
	
	private init() {
		
		cid_uid = UserDefaults.standard.string(forKey: "UID")
		
		if cid_uid == nil {
			cid_uid = UUID().uuidString
			UserDefaults.standard.set(cid_uid, forKey: "UID")
		}
		
		hardware = getHardware()
		
//		gameName! += " (\(device))"
//		print(gameName!)
//		print(version!)
		
	}

	
	func startAnalytics(_ model:String){

		gameName! += " (\(model))"
		let parameters: [String: Any] = [
			"v":"1",
			"t":"pageview",
			"tid": googleID,
//			"ua": device!,  //User Agent Override
			"cd1":device, //custom demention
			"cd2":hardware!,
			"cd3":model,
			"name": gameName!,
			"cid":cid_uid!,
			"dt":gameName!,
			"an":gameName!,
			"av":version!,
			"dp":gameName! //Document Path -- shows the Active Page "/Debris Field" instead of just "/"
			
		]
		
		sendData(parameters)
	
	}
	
	
	func sendEvent(_ eventAction:String){
        return;
		let parameters: [String: Any] = [
			"v":"1",
			"t":"event",
			"tid": googleID,
//			"ua": device!,
			"cd1":device, //custom demention
			"cd2":hardware!,
			"cid":cid_uid!,
			"ec": gameName!, //event category
			"ea": eventAction, //event action
			"dt":gameName!,  // documnet title
			"an":gameName!,
			"av":version!,
			"dp":gameName! //Document Path -- shows the Active Page "/Debris Field" instead of just "/"
		]
		
		sendData(parameters)

	}
	
	func sendData(_ parameters:[String: Any]){
		
		DispatchQueue.global(qos: .utility).async{
			let url = URL(string: "https://www.google-analytics.com/collect?")!
			var request = URLRequest(url: url)
			request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
			request.httpMethod = "POST"
			
			request.httpBody = parameters.percentEncoded()
			
			let task = URLSession.shared.dataTask(with: request) { data, response, error in
				guard let response = response as? HTTPURLResponse,
					  error == nil else {                                              // check for fundamental networking error
					print("error", error ?? "Unknown error")
					return
				}
				
				guard (200 ... 299) ~= response.statusCode else {                    // check for http errors
					print("statusCode should be 2xx, but is \(response.statusCode)")
					print("response = \(response)")
					return
				}
				
				//let responseString = String(data: data, encoding: .utf8)
				//print("responseString = \(responseString)")
			}
			
			task.resume()
		}
		
	}
	
	
	// Look at this later-> https://www.cocoawithlove.com/blog/2016/03/08/swift-wrapper-for-sysctl.html
	
	func getHardware() -> String{
		var systemInfo = utsname()
		uname(&systemInfo)
		
		let modelCode = withUnsafePointer(to: &systemInfo.machine) {
			$0.withMemoryRebound(to: CChar.self, capacity: 1) {
				ptr in String.init(validatingUTF8: ptr)
			}
		}
		
		
		return modelCode ?? "Unknown"
	}
	
}

extension Dictionary {
	func percentEncoded() -> Data? {
		return map { key, value in
			let escapedKey = "\(key)".addingPercentEncoding(withAllowedCharacters: .urlQueryValueAllowed) ?? ""
			let escapedValue = "\(value)".addingPercentEncoding(withAllowedCharacters: .urlQueryValueAllowed) ?? ""
			return escapedKey + "=" + escapedValue
		}
		.joined(separator: "&")
		.data(using: .utf8)
	}
}

extension CharacterSet {
	static let urlQueryValueAllowed: CharacterSet = {
		let generalDelimitersToEncode = ":#[]@" // does not include "?" or "/" due to RFC 3986 - Section 3.4
		let subDelimitersToEncode = "!$&'()*+,;="
		
		var allowed = CharacterSet.urlQueryAllowed
		allowed.remove(charactersIn: "\(generalDelimitersToEncode)\(subDelimitersToEncode)")
		return allowed
	}()
}


extension Bundle {
	var displayName: String? {
		return object(forInfoDictionaryKey: "CFBundleName") as? String // CFBundleDisplayName for ios
	}
	var version: String? {
		return object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String
	}
}
