//
//  SettingsScene.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit
import GameKit

class PositionButtonsScene: FoundationScene{
    
    
    override func sceneDidLoad() {
        scaleMode = .aspectFit
        super.sceneDidLoad()
        GKAccessPoint.shared.isActive = false
        #if os(tvOS)
          SceneManager.shared.vc?.controllerUserInteractionEnabled = false
        #endif
    }
    

    override func buttonPressed(_ buttonName: String /*controllerItem*/,button:MenuButtonNode?) {
        if buttonName == "Button B" {//controllerItem.btn_b{
            GameData.playSound(GameData.sound_buttonBack)
            SceneManager.shared.gotoScene("MenuScene")
        }
    }
    
}
