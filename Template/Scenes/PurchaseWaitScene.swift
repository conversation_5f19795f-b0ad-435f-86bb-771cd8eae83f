//
//  SettingsScene.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit
import GameKit

class PurchaseWaitScene: FoundationScene {
	
   
	override func sceneDidLoad() {
		scaleMode = .aspectFit

		super.sceneDidLoad()
//#if os(iOS)
//        SceneManager.shared.vc?.hideAd(0.5)
//#endif
        GKAccessPoint.shared.isActive = false
        
        //setUpSprites()
        
		#if os(tvOS)
            SceneManager.shared.vc?.controllerUserInteractionEnabled = false
		#endif
	}
    override func transitionComplete() {
        super.transitionComplete()
        GameState.currentState = .loadPurchase
    }

    override func buttonPressed(_ buttonName: String/*controllerItem*/,button:MenuButtonNode?) {
        super.buttonPressed(buttonName, button: button)
        if buttonName == "Button B" /*controllerItem.btn_b*/{
            GameData.playSound(GameData.sound_buttonBack)
            SceneManager.shared.gotoScene("MenuScene")
        }
    }
    
}
