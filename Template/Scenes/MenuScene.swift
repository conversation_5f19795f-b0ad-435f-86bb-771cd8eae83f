//
//  MenuScene.swift
//  De<PERSON><PERSON> Field
//
//  Created by <PERSON> on 3/13/21.
//

import GameplayKit
import SpriteKit
import GameKit

class MenuScene: FoundationScene {
    

    class func newMenuScene() -> MenuScene {
        // Load 'GameScene.sks' as an SKScene.
        guard let scene = SKScene(fileNamed: "MenuScene") as? MenuScene else {
            print("Failed to load MenuScene.sks")
            abort()
        }
        
        // Set the scale mode to scale to fit the window
        //scene.scaleMode = .fill
        
        return scene
    }
    
    
    override func transitionComplete() {
        super.transitionComplete()
        GameState.currentState = .menu
        GKAccessPoint.shared.isActive = false
        //SceneManager.shared.markLogo.isHidden = false
//#if os(iOS)
//        SceneManager.shared.vc?.showAd(0.25)
//#endif
        
    }
    
    override func sceneDidLoad() {
        scaleMode = .aspectFit// .fill
        super.sceneDidLoad()
        
        let vNode = childNode(withName: "version") as? SKLabelNode
        vNode?.text = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String

//      GKAccessPoint.shared.location = .bottomLeading
//        GKAccessPoint.shared.showHighlights = false
//        GKAccessPoint.shared.isActive = true
        
        
        #if os(tvOS)
            SceneManager.shared.vc?.controllerUserInteractionEnabled = true
        #endif

    }
    
}//end class
