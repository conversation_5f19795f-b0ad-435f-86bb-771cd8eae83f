//
//  SettingsScene.swift
//  Debris Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit
import GameKit

class HelpScene: FoundationScene {
    
    var blackhole:SKEmitterNode?
    var superNova:SKEmitterNode?
    
    let blockHoleLoc = CGPoint(x:200,y:500)
    let superNovaLoc = CGPoint(x:800,y:500)
    
    
   
    override func sceneDidLoad() {
        scaleMode = .aspectFit

        super.sceneDidLoad()
//#if os(iOS)
//        SceneManager.shared.vc?.hideAd(0.5)
//#endif
        GKAccessPoint.shared.isActive = false
        
        //setUpSprites()
        
        #if os(tvOS)
            SceneManager.shared.vc?.controllerUserInteractionEnabled = false
        #endif
    }
    override func transitionComplete() {
        super.transitionComplete()
        GameState.currentState = .help
    }

    override func buttonPressed(_ buttonName: String/*controllerItem*/,button:MenuButtonNode?) {
        super.buttonPressed(buttonName, button: button)
        if buttonName == "Button B" /*controllerItem.btn_b*/{
            GameData.playSound(GameData.sound_buttonBack)
            SceneManager.shared.gotoScene("MenuScene")
        }
    }
    
    
//    func setUpSprites(){
//
//
//        superNova = SKEmitterNode.init(fileNamed: "Supernova.sks")
//        superNova?.position = blockHoleLoc;
//        superNova?.run(SKAction.repeat(SKAction.rotate(byAngle: 1, duration: 5.0), count: -1))
//        superNova?.run(SKAction.scale(to: 5.0, duration: 10))
//        superNova?.setScale(0.2)
//
//        self.addChild(superNova!)
//
//        blackhole = SKEmitterNode.init(fileNamed: "blackhole.sks")
//        blackhole?.position = superNovaLoc
//        blackhole?.setScale(0.75)
//        blackhole?.run(SKAction.repeat(SKAction.rotate(byAngle: 1, duration: 5.0), count: -1))
//        self.addChild(blackhole!)
//
//    }
    
}
