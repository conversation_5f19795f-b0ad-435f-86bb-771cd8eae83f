//
//  KeyboardInput+Foundation.swift
//  Template
//
//  Created by <PERSON> on 4/19/21.
//

import SpriteKit
import GameKit
extension FoundationScene{
	#if os(OSX)
	
//	func test(){
//		if let keyboard = GCKeyboard.coalesced?.keyboardInput{
//			
//			keyboard.keyChangedHandler = {
//				(keyboard,key,keyCode,pressed) in
//				
//				var f = self.theControllerInputDelegate!.buttonReleased
//				if pressed{
//					f = self.theControllerInputDelegate!.buttonPressed
//				}
//				let btn = self.uiButtons[self.index]
//				
//				switch keyCode {
//				case GCKeyCode.leftArrow: //left
//					
//					f("Direction Pad Left", btn)
//					
//				case GCKeyCode.rightArrow: //right
//					f("Direction Pad Right", btn)
//					
//				case GCKeyCode.downArrow: //down
//					f("Direction Pad Down", btn)
//					
//				case GCKeyCode.upArrow: //up
//					f("Direction Pad Up", btn)
//					
//				case GCKeyCode.spacebar:
//					f("Button A", btn)
//				case GCKeyCode.returnOrEnter:
//					f("Button B", btn)
//					
//				default: break
//					
//				}
//				
//			}
//		}
//	}
	
 override func keyUp(with event: NSEvent) {

		let btn = uiButtons[index]

		switch event.keyCode {
		case 123: //left
			theControllerInputDelegate?.buttonReleased("Direction Pad Left"/*controllerItem.dpad_left*/,button: btn)

		case 124: //right
			theControllerInputDelegate?.buttonReleased("Direction Pad Right"/*controllerItem.dpad_right*/,button: btn)

		case 125: //down
			theControllerInputDelegate?.buttonReleased("Direction Pad Down"/*controllerItem.dpad_down*/,button: btn)

		case 126: //up
			theControllerInputDelegate?.buttonReleased("Direction Pad Up"/*controllerItem.dpad_up*/,button: btn)

		case 49:
			theControllerInputDelegate?.buttonReleased("Button A"/*controllerItem.btn_a*/,button: btn)
		case 36:
			theControllerInputDelegate?.buttonReleased("Button B"/*controllerItem.btn_b*/,button: btn)

		default: break

		}

		switch event.characters! as String {
		case "p":
			theControllerInputDelegate?.buttonReleased("Button Menu"/*controllerItem.btn_menu*/,button: btn!)
		default:
			break;
		}
	}
	override func keyDown(with event: NSEvent) {

		let btn = uiButtons[index]
		switch event.keyCode {
		case 123: //left
			theControllerInputDelegate?.buttonPressed("Direction Pad Left"/*controllerItem.dpad_left*/,button: btn)
		case 124: //right
			theControllerInputDelegate?.buttonPressed("Direction Pad Right"/*controllerItem.dpad_right*/,button: btn)
		case 125: //down
			theControllerInputDelegate?.buttonPressed("Direction Pad Down"/*controllerItem.dpad_down*/,button: btn)
		case 126: //up
			theControllerInputDelegate?.buttonPressed("Direction Pad Up"/*controllerItem.dpad_up*/,button: btn)
		case 49:
			theControllerInputDelegate?.buttonPressed("Button A"/*controllerItem.btn_a*/,button: btn)
		case 36:
			theControllerInputDelegate?.buttonPressed("Button B"/*controllerItem.btn_b*/,button: btn)
		default: break
		}
	}
	
	#endif
}
