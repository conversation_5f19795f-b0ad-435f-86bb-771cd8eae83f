//
//  SettingsScene.swift
//  Debri<PERSON> Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit
import GameKit


class SettingsScene: FoundationScene {
    
    
    override func sceneDidLoad() {
        scaleMode = .aspectFit
        super.sceneDidLoad()
//#if os(iOS)
//        SceneManager.shared.vc?.hideAd(0.5) 
//#endif
        
        GameState.currentState = .settings
        GKAccessPoint.shared.isActive = false
        #if os(tvOS)
            SceneManager.shared.vc?.controllerUserInteractionEnabled = false
        #endif
        
//#if !os(iOS)
//        if let repo = childNode(withName: "repoButton") as? UIButton_NavigateToScene {
//            repo.alpha = 0.0
//        }
//#endif
        
    }
    
  
    @objc func yourSelector(){
        print("FD")
    }
    
    override func buttonPressed(_ buttonName: String/*controllerItem*/,button:MenuButtonNode?) {
        super.buttonPressed(buttonName, button: button)
        if buttonName == "But<PERSON> B" /*controllerItem.btn_b*/{
            GameData.playSound(GameData.sound_buttonBack)
            SceneManager.shared.gotoScene("MenuScene")
        }
    }
    
}
