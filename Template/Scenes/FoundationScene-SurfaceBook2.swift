//
//  foundationScene.swift
//  De<PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/1/21.
//

import SpriteKit
import GameKit

class FoundationScene: SKScene,controllerInputDelegate,transitionDelegate{
	
	var rolloverNode:SKSpriteNode?
	
	var theUIButtonDelegate:UIButtonDelegate?

	var theControllerInputDelegate:controllerInputDelegate?
	
	#if os(tvOS)
	 var tapRecognizer:UITapGestureRecognizer?
	#endif
	
	
	//struct menuButtons {
	
	var uiButtons = [Int:MenuButtonNode]()
	var index = 1 {
		
		willSet(newIndex) {
			if (newIndex == -1){
				return
			}
			
			if newIndex != uiButtons.count{
					uiButtons[index]?.onDeactivate()
			}
			
		}
		
		didSet(oldIndex) {
			
			if index < 0 { index = 0 }
			if index > uiButtons.count-1 { index = uiButtons.count-1 }
			
			if (oldIndex != index){
					uiButtons[index]?.onActivate()
			}
		}
		
	}
	
	var virtualButtons:[VirtualButton]?
	
	
	func resetIndex(){
		index = (self.userData?["defaultButtonIndex"] as? Int)!
		uiButtons[index]?.onActivate()
	}
	
	override func sceneDidLoad() {
		scaleMode = .aspectFit
		super.sceneDidLoad() //new
		
		
	}
	
	deinit {
		theControllerInputDelegate = nil
		theUIButtonDelegate = nil
		NotificationCenter.default.removeObserver(self, name: NSNotification.Name.GCControllerDidConnect, object: nil)
		NotificationCenter.default.removeObserver(self, name: NSNotification.Name.GCControllerDidDisconnect, object: nil)
		NotificationCenter.default.removeObserver(self, name: NSNotification.Name.GCControllerDidBecomeCurrent, object: nil)

	}
	
	override func didMove(to view: SKView) {
		//tapRecognizer = UITapGestureRecognizer(target: self, action: #selector(self.tappedPlayPause(sender:)))
		//		tapRecognizer?.allowedPressTypes =  [NSNumber(value:UIPress.PressType.playPause.rawValue),
		//											 NSNumber(value:UIPress.PressType.select.rawValue)	]
		//
		//		SceneManager.shared.mainSKView!.addGestureRecognizer(tapRecognizer!)
		
		//let screenSize: CGRect = UIScreen.main.bounds
		
		//setUpGameCenterViewIndicatorView()
		
		SceneManager.shared.currentScene = scene
		theControllerInputDelegate = self
		
		//test()
		
		#if os(tvOS)
		//SceneManager.shared.vc?.controllerUserInteractionEnabled = true
		#endif
		setUpControllers()
		
		let defautUIButtonIndex = self.userData?["defaultButtonIndex"] as? Int
		index = defautUIButtonIndex ?? 0
		for child in self.children {
			if let uiButtonNode = child as? MenuButtonNode {
				uiButtonNode.theUIButtonDelegate = uiButtonNode /*scene*/ as? UIButtonDelegate //problem
				uiButtons[uiButtonNode.index!] = uiButtonNode
			}
		}
		index = defautUIButtonIndex ?? 0
		
	}
	
	override func willMove(from view: SKView) {
		SceneManager.shared.theTransitionDelegate!.transitionComplete()
	}
	
	func transitionComplete() {}
	
	// ==================================================================================================
	// ==================================================================================================
	
	func setUpControllers(){
		
//		if GameData.farts == true{
//			return
//		}
		
		NotificationCenter.default.addObserver(self, selector: #selector(self.controllerDidConnect),
											   name: NSNotification.Name.GCControllerDidConnect, object: nil)
		
		NotificationCenter.default.addObserver(self, selector: #selector(self.controllerDisconnected),
											   name: NSNotification.Name.GCControllerDidDisconnect, object: nil)
		
		NotificationCenter.default.addObserver(self, selector: #selector(self.controllerDidBecomeCurrent),
											   name: NSNotification.Name.GCControllerDidBecomeCurrent, object: nil)

		guard let controller = GCController.controllers().first else {
			return
		}
		
		setUpConrollerFunc(controller)
		
	}
	
	func setUpConrollerFunc(_ controller:GCController){

		let pressedHandler: GCControllerButtonValueChangedHandler = { [unowned self] button, value, pressed in
			
			/// no controllerDelegate no play!
			guard (self.theControllerInputDelegate != nil) else{
				return
			}
			
//			if (GKAccessPoint.shared.isPresentingGameCenter){
//				return
//			}
			/// Swift lets us put functions into variables!
			var controlerFunc = self.theControllerInputDelegate!.buttonReleased
			if pressed {
				controlerFunc = self.theControllerInputDelegate!.buttonPressed
			}
			
			
			if let key = controller.physicalInputProfile.buttons.getKey(forValue: button) {
				/// Here I get the name of the button through it aliases property
				/// The buttonMap is a dictionary that maps the name to the classes controllerItem
				/// and I send the button event with the button that was pressed to the scene
				//				let it = button.aliases.joined() // not sure if this is correct or even how to really use joined!
				let uiBtn = uiButtons[index]
				controlerFunc(key/*buttonMap[it]!*/, uiBtn)
			}
		}
		
		/// Every button gets the pressCjamgedHandler!
		/// ugly as fuck but it works and it's quick (so far)
		if let gamepad = controller.extendedGamepad {
			gamepad.buttonA.pressedChangedHandler    = pressedHandler
			gamepad.buttonB.pressedChangedHandler    = pressedHandler
			gamepad.buttonX.pressedChangedHandler    = pressedHandler
			gamepad.buttonY.pressedChangedHandler    = pressedHandler
			gamepad.dpad.up.pressedChangedHandler    = pressedHandler
			gamepad.dpad.down.pressedChangedHandler  = pressedHandler
			gamepad.dpad.left.pressedChangedHandler  = pressedHandler
			gamepad.dpad.right.pressedChangedHandler = pressedHandler
			
			
			gamepad.buttonMenu.pressedChangedHandler 			 = pressedHandler
			gamepad.buttonHome?.pressedChangedHandler 			 = pressedHandler
			gamepad.buttonOptions?.pressedChangedHandler 		 = pressedHandler
			gamepad.rightThumbstickButton?.pressedChangedHandler = pressedHandler
			gamepad.rightTrigger.pressedChangedHandler 			 = pressedHandler
			gamepad.rightShoulder.pressedChangedHandler 		 = pressedHandler
			gamepad.leftThumbstickButton?.pressedChangedHandler  = pressedHandler
			gamepad.leftTrigger.pressedChangedHandler 			 = pressedHandler
			gamepad.leftShoulder.pressedChangedHandler 			 = pressedHandler
			
		}
		
//		if let gamepad = controller.microGamepad {
//			gamepad.buttonA.pressedChangedHandler    = pressedHandler
//			gamepad.buttonX.pressedChangedHandler    = pressedHandler
//
//			gamepad.dpad.up.pressedChangedHandler    = pressedHandler
//			gamepad.dpad.down.pressedChangedHandler  = pressedHandler
//			gamepad.dpad.left.pressedChangedHandler  = pressedHandler
//			gamepad.dpad.right.pressedChangedHandler = pressedHandler
//
//			gamepad.buttonMenu.pressedChangedHandler 			 = pressedHandler
//
//		}
	} ///end setUpConrollerFunc
	
	func buttonPressed(_ buttonName: String /*controllerItem*/, button: MenuButtonNode?) {
		
		switch buttonName {
		case "Direction Pad Right":
			index += 1
		case "Direction Pad Left":
			index -= 1
		case "Direction Pad Up":
			index += 4
		case "Direction Pad Down":
			index -= 4
		case "Button A":
			uiButtons[index]!.onButtonDown()
			break;
		default: break
		} ///end switch
		
	}
	
	func buttonReleased(_ buttonName: String, button: MenuButtonNode?) {
		switch buttonName {
		case "Button A":
			uiButtons[index]!.onButtonUp()
			break;
		default: break
		} ///end switch
	}
	
	
	// MARK: GCGameController Notification Handling
	
	@objc func controllerDidBecomeCurrent(_ notification:Notification){
		guard let controller = notification.object as? GCController else {
			return
		}
		if (GameData.theGamePad == controller){
			setUpConrollerFunc(controller)
		}
	}
	@objc func controllerDidConnect(_ notification:Notification) {

		guard let controller = notification.object as? GCController else {
			return
		}
		
//		if controller.productCategory.contains("Remote"){
//
//		}else{
		if (GameData.theGamePad == nil){
			if ((controller.extendedGamepad) != nil){
				print("\(controller.vendorName ?? "Unknown") connected")
				GameData.theGamePad = controller
				setUpConrollerFunc(controller)
				
				dispayControllerConnectedIcon(true,controller:controller)
				
				//NotificationCenter.default.post(name: Notification.Name(rawValue: "controllerDidConnect"), object: nil)
			}
		}
//		}

//		if controller.extendedGamepad == nil {
//			print("micro connected")
//		}
				
	}
	
	
	@objc func controllerDisconnected(_ notification:Notification) {

		guard let controller = notification.object as? GCController else {
			return
		}
//		if controller.productCategory.contains("Remote"){
//
//		}else{
			if controller.extendedGamepad != nil {
				if (GameData.theGamePad == controller){
				// PAUSE THE GAME!
				// send a notification that it's been diconnected
				// will need to set up the paused UI if game is playing
				// otherwise I need to pop up a little icon disconnected icon
				GameState.currentState = GameState.states.paused
				print("\(controller.vendorName ?? "Unknown") DISCONNECTED!!!!!")
				dispayControllerConnectedIcon(false,controller:controller)
				GameData.theGamePad = nil
				//NotificationCenter.default.post(name: Notification.Name(rawValue: "controllerDisconnected"), object: nil)
			}
	}
//		if ((controller.extendedGamepad) == nil){
//			print("micro disconnected")
//		}
		
	}
	
	func dispayControllerConnectedIcon(_ connected:Bool,controller:GCController){
		
		if controller.productCategory.contains("Remote"){
			
		}else{
			GameData.controllerConnected = connected
			
			var theImage = "controllerConnected.png"
			var disText:String = " Connected"
			if (!connected){
				theImage = "controllerDisconnected.png"
				disText = " Disconnected"
			}
			let icon = SKSpriteNode(imageNamed: theImage)
			icon.alpha = 0.0
			icon.position.x = (SceneManager.shared.currentScene?.frame.size.width)!/2
			icon.position.y = (SceneManager.shared.currentScene?.frame.size.height)!-100.0
			
			let label = SKLabelNode(text: (controller.vendorName ?? "Controller") + disText)
			icon.addChild(label)
			
			label.position.y -= 70
			SceneManager.shared.currentScene?.addChild(icon)
			
			
			let fadein = SKAction.fadeAlpha(to: 1.0, duration: 0.15)
			let wait = SKAction.wait(forDuration: 5.0)
			let fadeOut = SKAction.fadeAlpha(to: 0.0, duration: 0.15)
			let kill = SKAction.removeFromParent()
			let seq = SKAction.sequence([fadein,wait,fadeOut,kill])
			
			icon.run(seq)
		}
	}
	
	#if os(tvOS)
	@objc func tappedPlayPause(sender: UITapGestureRecognizer){
		self.theControllerInputDelegate?.buttonReleased("Button Menu", button: nil)
		
	}
	func handleTap(sender: UITapGestureRecognizer) {
		if sender.state == .ended {
			// handling code
		}
	}
	#endif
	
	func setUpGameCenterViewIndicatorView(){
		
		let y = (SceneManager.shared.mainSKView?.frame.size.height)! - 53
		#if os(OSX)
		let myView = NSView(frame: CGRect(x: 13, y: y, width: 60, height: 60))
		myView.layer?.backgroundColor = NSColor.orange.cgColor
		myView.wantsLayer = true
		#else
		let myView = UIView(frame: CGRect(x: 13, y: y, width: 60, height: 60))
		myView.backgroundColor = UIColor.orange
		#endif
		myView.translatesAutoresizingMaskIntoConstraints = false
		//myView.layer?.cornerRadius = 30.0
		SceneManager.shared.mainSKView?.addSubview(myView)
		
		
		
		myView.topAnchor.constraint(equalTo: SceneManager.shared.mainSKView!.safeAreaLayoutGuide.topAnchor).isActive = true
		
		myView.leftAnchor.constraint(equalTo: SceneManager.shared.mainSKView!.safeAreaLayoutGuide.leftAnchor,constant: 13.0).isActive = true
		
		
		myView.widthAnchor.constraint(equalToConstant: 60).isActive = true
		myView.heightAnchor.constraint(equalToConstant: 60).isActive = true
		
	}
	#if os(OSX)
	var over = false
	var previousBtn:MenuButtonNode?
	
	override func update(_ currentTime: TimeInterval) {
	
		
		// contains point in WINDOW!
		let eventp = SceneManager.shared.currentScene!.convertPoint(fromView: (SceneManager.shared.mainSKView?.window!.mouseLocationOutsideOfEventStream)!)

		let node:MenuButtonNode? = self.atPoint(eventp) as? MenuButtonNode
		
		if (node != nil){
			if over == false{
				if uiButtons.values.contains(node!){
					previousBtn = node!
					previousBtn?.onMouseEnter()
					over = true
				}
			}
		}else{
			if (over == true){
				previousBtn?.onMouseExit()
				over = false
			}
			
		}
	}
	#endif


}//end FoundationScene


extension Dictionary where Value: Equatable {
	func getKey(forValue val: Value) -> Key? {
		return first(where: { $1 == val })?.key
	}
}
