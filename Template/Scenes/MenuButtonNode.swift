//
//  Button.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 3/31/21.
//

import SpriteKit

class MenuButtonNode:SKSpriteNode {

    // MARK: - Constants
    static let DEFAULT_CIRCLE_COLOR_R: CGFloat = 0.25
    static let DEFAULT_CIRCLE_COLOR_G: CGFloat = 0.25
    static let DEFAULT_CIRCLE_COLOR_B: CGFloat = 0.25
    static let DEFAULT_CIRCLE_COLOR_A: CGFloat = 0.5
    
    static let DEFAULT_SYMBOL_COLOR_R: CGFloat = 1.0
    static let DEFAULT_SYMBOL_COLOR_G: CGFloat = 1.0
    static let DEFAULT_SYMBOL_COLOR_B: CGFloat = 1.0
    static let DEFAULT_SYMBOL_COLOR_A: CGFloat = 1.0
    
    static let DEFAULT_SYMBOL_SIZE_RATIO: CGFloat = 0.7
    
    static let HIGHLIGHT_COLOR_R: CGFloat = 248.0/255.0
    static let HIGHLIGHT_COLOR_G: CGFloat = 185.0/255.0
    static let HIGHLIGHT_COLOR_B: CGFloat = 0.0
    static let HIGHLIGHT_COLOR_A: CGFloat = 0.7
    
    static let RESET_COLOR_R: CGFloat = 255.0/255.0
    static let RESET_COLOR_G: CGFloat = 255.0/255.0
    static let RESET_COLOR_B: CGFloat = 255.0/255.0
    
    static let CIRCLE_Z_POSITION: CGFloat = -1
    static let SYMBOL_Z_POSITION: CGFloat = 1

    let SYMBOL_WEIGHT: UIImage.SymbolWeight = .thin
    
    var theUIButtonDelegate:UIButtonDelegate?
    var theControllerDelegate:controllerInputDelegate?

    var isVirtualButton = false;
    var index:Int?
    var isFocusable = true;
    var isPressed = false;
    var over = false;
    var active = false;
    var ignore = false;
    var offset:CGPoint?
    var theButton:String?
    
    // Circle and symbol properties
    var circleShape: SKShapeNode?
    private var symbolSprite: SKSpriteNode?
    private var originalCircleColor: SKColor = .white

    // Store original color properties (before they get modified in init)
    private var originalColor: SKColor = .white
    private var originalColorBlendFactor: CGFloat = 0.0
    private var originalAlpha: CGFloat = 1.0

    // Toggle functionality properties
    private var useToggle: Bool = false
    private var toggleKey: String?
    private var isToggleOn: Bool = true
    private var originalTextureName: String?
    
    override init(texture: SKTexture?, color: SKColor, size: CGSize) {
        super.init(texture: texture, color: color, size: size)
        isUserInteractionEnabled = true
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        
        // Capture original color properties BEFORE they get modified
        originalColor = self.color
        originalColorBlendFactor = self.colorBlendFactor
        originalAlpha = self.alpha
        print("🎨 INIT: Captured original color: \(originalColor), BlendFactor: \(originalColorBlendFactor), Alpha: \(originalAlpha)")
        
        isUserInteractionEnabled = true
        index = userData?["index"] as? Int ?? -1

        #if !os(iOS)
            let onlyShowOnIOS = userData?["iosOnly"] as? Bool ?? false
            isHidden = onlyShowOnIOS;
        #endif
        
        if self.color != SKColor.white {
            // Make transparent since color was set in Xcode
            self.color = SKColor.clear
            print("🎨 INIT: Color was not white, setting to clear. Original was: \(originalColor)")
        }
        
        // Check for toggle functionality
        setupToggleFromUserData()

        // Check for circle and symbol setup from userData
        setupFromUserData()
    }

    private func setupToggleFromUserData() {
        // Check if this button should have toggle functionality
        useToggle = userData?["useToggle"] as? Bool ?? false

        print("🔄 Button \(name ?? "unnamed") - useToggle: \(useToggle)")

        if useToggle {
            // Get toggle key (fallback to button name)
            toggleKey = userData?["toggleKey"] as? String ?? name

            // Load saved toggle state (default to true/ON)
            if let key = toggleKey {
                isToggleOn = UserDefaults.standard.object(forKey: key) == nil ? true : UserDefaults.standard.bool(forKey: key)
            }

            // Store original texture name if it exists
            if let currentTexture = texture {
                originalTextureName = currentTexture.description
            }

            print("🔄 Toggle button setup - Name: \(name ?? "nil"), Key: \(toggleKey ?? "nil"), State: \(isToggleOn)")
            print("🔄 Original texture: \(originalTextureName ?? "none")")
            print("🔄 SF Symbol: \(userData?["sfSymbol"] as? String ?? "none")")
            print("🔄 SF Symbol Off: \(userData?["sfSymbolOff"] as? String ?? "none")")
        }
    }

    private func updateToggleVisuals() {
        guard useToggle else { return }

        print("🔄 updateToggleVisuals() - isToggleOn: \(isToggleOn)")

        if isToggleOn {
            // ON state - use original/chosen colors
            print("🔄 Updating to ON state")
            updateToggleVisualsForOnState()
        } else {
            // OFF state - use red color
            print("🔄 Updating to OFF state")
            updateToggleVisualsForOffState()
        }
    }

    private func updateToggleVisualsForOnState() {
        // Restore original colors and textures
        if circleShape != nil {
            // Circle button - restore original circle color
            circleShape?.fillColor = originalCircleColor
        }
        // For regular buttons, don't change background color - only symbol/texture color

        // Handle texture/symbol for ON state
        updateToggleTexture(isOn: true)
    }

    private func updateToggleVisualsForOffState() {
        // Apply red color for OFF state
        if circleShape != nil {
            // Circle button - make circle red
            #if os(OSX)
            circleShape?.fillColor = NSColor.systemRed
            #else
            circleShape?.fillColor = UIColor.systemRed
            #endif
        }
        // For regular buttons, don't change background color - only symbol/texture color

        // Handle texture/symbol for OFF state
        updateToggleTexture(isOn: false)
    }

    private func updateToggleTexture(isOn: Bool) {
        print("🔄 updateToggleTexture() - isOn: \(isOn)")
        print("🔄 originalTextureName: \(originalTextureName ?? "none")")
        print("🔄 sfSymbol: \(userData?["sfSymbol"] as? String ?? "none")")
        print("🔄 sfSymbolOff: \(userData?["sfSymbolOff"] as? String ?? "none")")

        if isOn {
            // ON state - use original texture or sfSymbol with original colors
            if let originalTexture = originalTextureName {
                print("🔄 Using original texture: \(originalTexture)")
                // Try to restore original texture
                texture = SKTexture(imageNamed: originalTexture)
                // Restore original button color for texture
                self.color = originalColor
                self.colorBlendFactor = originalColorBlendFactor
            } else if let sfSymbol = userData?["sfSymbol"] as? String {
                print("🔄 Using SF Symbol ON: \(sfSymbol)")
                setupToggleSFSymbol(symbolName: sfSymbol, useRedColor: false)
            }
        } else {
            // OFF state - use texture_off or sfSymbolOff with red color
            if let originalTexture = originalTextureName {
                let offTextureName = originalTexture + "_off"
                print("🔄 Using OFF texture: \(offTextureName)")
                texture = SKTexture(imageNamed: offTextureName)
                // Apply red color to texture
                #if os(OSX)
                self.color = NSColor.systemRed
                #else
                self.color = UIColor.systemRed
                #endif
                self.colorBlendFactor = 1.0
            } else if let sfSymbolOff = userData?["sfSymbolOff"] as? String {
                print("🔄 Using SF Symbol OFF: \(sfSymbolOff)")
                setupToggleSFSymbol(symbolName: sfSymbolOff, useRedColor: true)
            }
        }
    }

    private func setupToggleSFSymbol(symbolName: String, useRedColor: Bool = false) {
        print("🔄 setupToggleSFSymbol() - symbol: \(symbolName), useRedColor: \(useRedColor)")

        // Remove existing symbol
        symbolSprite?.removeFromParent()

        let symbolColor: SKColor
        if useRedColor {
            // Use red color for OFF state
            #if os(OSX)
            symbolColor = NSColor.systemRed
            #else
            symbolColor = UIColor.systemRed
            #endif
            print("🔄 Using red color for symbol")
        } else {
            // Use normal color for ON state
            symbolColor = getEffectiveSymbolColor()
            print("🔄 Using normal color for symbol: \(symbolColor)")
        }

        let symbolSize = userData?["symbolSize"] as? CGFloat ?? (frame.width * MenuButtonNode.DEFAULT_SYMBOL_SIZE_RATIO)
        let configuration = UIImage.SymbolConfiguration(pointSize: symbolSize, weight: SYMBOL_WEIGHT)

        print("🔄 Symbol size: \(symbolSize)")

        #if os(iOS)
        if let symbolImage = UIImage(systemName: symbolName, withConfiguration: configuration) {
            let uiColor = symbolColor as UIColor

            let renderer = UIGraphicsImageRenderer(size: symbolImage.size)
            let coloredImage = renderer.image { context in
                uiColor.setFill()
                symbolImage.withTintColor(uiColor, renderingMode: .alwaysTemplate).draw(at: .zero)
            }

            let texture = SKTexture(image: coloredImage)
            symbolSprite = SKSpriteNode(texture: texture)
            symbolSprite?.position = CGPoint.zero
            symbolSprite?.zPosition = MenuButtonNode.SYMBOL_Z_POSITION

            if let sprite = symbolSprite {
                addChild(sprite)
            }
        }
        #endif
    }

    public func toggleState() {
        print("🔄 toggleState() called - useToggle: \(useToggle)")
        guard useToggle else {
            print("❌ Toggle not enabled, returning")
            return
        }

        print("🔄 Before toggle - State: \(isToggleOn)")

        // Toggle the state
        isToggleOn.toggle()

        print("🔄 After toggle - State: \(isToggleOn)")

        // Save to UserDefaults
        if let key = toggleKey {
            UserDefaults.standard.set(isToggleOn, forKey: key)
            print("🔄 Saved to UserDefaults - Key: \(key), Value: \(isToggleOn)")
        }

        // Update visuals
        updateToggleVisuals()

        print("🔄 Button toggled - Key: \(toggleKey ?? "nil"), New State: \(isToggleOn)")
    }



    private func setupFromUserData() {
        // Debug output
        //print("🔍 Setting up from userData for: \(name ?? "unnamed")")
        //print("🔍 UserData: \(userData ?? [:])")
        
        // Check if this should be a circle button
        let useCircle = userData?["useCircle"] as? Bool ?? false
        //print("🔍 useCircle: \(useCircle)")
        
        if useCircle {
            // Store the original texture before hiding it
            let originalTexture = self.texture
            
            print("🎨 Using stored original color: \(originalColor), BlendFactor: \(originalColorBlendFactor), Alpha: \(originalAlpha)")
            
            // Hide the original texture
            texture = nil
            //print("🔍 Texture set to nil")
            
            // Get circle properties from userData (default to semi-transparent blue circle)
            let circleColorR = userData?["circleColorR"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_R
            let circleColorG = userData?["circleColorG"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_G
            let circleColorB = userData?["circleColorB"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_B
            let circleColorA = userData?["circleColorA"] as? CGFloat ?? MenuButtonNode.DEFAULT_CIRCLE_COLOR_A
            
            #if os(OSX)
            let circleColor = NSColor(red: circleColorR, green: circleColorG, blue: circleColorB, alpha: circleColorA)
            #else
            let circleColor = UIColor(red: circleColorR, green: circleColorG, blue: circleColorB, alpha: circleColorA)
            #endif
            
            originalCircleColor = circleColor
            //print("🔍 Circle color: R:\(circleColorR) G:\(circleColorG) B:\(circleColorB) A:\(circleColorA)")
            
            // Create circle
            let radius = min(frame.width, frame.height) / 2
            //print("🔍 Frame size: \(frame.size), Radius: \(radius)")
            setupCircle(radius: radius, color: circleColor)
            
            // Setup texture first (highest priority), then SF Symbol as fallback
            if let existingTexture = originalTexture {
                //print("🔍 Using existing texture for symbol")
                
                // Use original sprite's color properties from attributes inspector
                let symbolColor = getEffectiveSymbolColor(from: originalColor, blendFactor: originalColorBlendFactor, alpha: originalAlpha)
                let symbolSize = userData?["symbolSize"] as? CGFloat
                //print("🔍 Using sprite color properties - Color: \(originalColor), BlendFactor: \(originalColorBlendFactor), Alpha: \(originalAlpha)")
                //print("🔍 Symbol size: \(symbolSize ?? -1)")
                setupTextureSymbol(texture: existingTexture, color: symbolColor, size: symbolSize)
                
            } else if let symbolName = userData?["sfSymbol"] as? String {
                //print("🔍 Setting up symbol: \(symbolName)")
                
                // Use original sprite's color properties from attributes inspector
                let symbolColor = getEffectiveSymbolColor(from: originalColor, blendFactor: originalColorBlendFactor, alpha: originalAlpha)
                let symbolSize = userData?["symbolSize"] as? CGFloat
                //print("🔍 Using sprite color properties for SF Symbol")
                //print("🔍 Symbol size: \(symbolSize ?? -1)")
                setupSFSymbol(symbolName: symbolName, color: symbolColor, size: symbolSize)
            } else {
                //print("🔍 No existing texture or sfSymbol found in userData")
            }
        } else {
            //print("🔍 useCircle is false, skipping circle setup")
        }

        // Apply toggle visuals after setup
        if useToggle {
            updateToggleVisuals()
        }
    }
    
    private func setupCircle(radius: CGFloat, color: SKColor) {
        //print("🔴 Setting up circle with radius: \(radius), color: \(color)")
        circleShape?.removeFromParent()
        
        circleShape = SKShapeNode(circleOfRadius: radius)
        circleShape?.fillColor = color
        circleShape?.strokeColor = .clear
        circleShape?.position = CGPoint.zero
        circleShape?.zPosition = MenuButtonNode.CIRCLE_Z_POSITION
        
        if let circle = circleShape {
            addChild(circle)
            //print("🔴 Circle added to parent. Circle frame: \(circle.frame)")
            //print("🔴 Parent frame: \(frame)")
            //print("🔴 Circle position: \(circle.position)")
        } else {
            //print("❌ Failed to create circle")
        }
    }
    
    private func setupTextureSymbol(texture: SKTexture, color: SKColor, size: CGFloat? = nil) {
        symbolSprite?.removeFromParent()
        
        // Create UIImage from the texture
        let cgImage = texture.cgImage()
        let image = UIImage(cgImage: cgImage)
        
        print("🔍 Original image size: \(image.size)")
        print("🔍 Tint color: \(color)")
        
        let symbolSize = size ?? (frame.width * MenuButtonNode.DEFAULT_SYMBOL_SIZE_RATIO)
        print("🔍 Target symbol size: \(symbolSize)")
        
        let targetSize = CGSize(width: symbolSize, height: symbolSize)
        
        // Simple approach - just create the sprite and tint it using SpriteKit's built-in tinting
        let newTexture = SKTexture(image: image)
        symbolSprite = SKSpriteNode(texture: newTexture)
        symbolSprite?.size = targetSize  // Resize to target size
        symbolSprite?.position = CGPoint.zero
        symbolSprite?.zPosition = MenuButtonNode.SYMBOL_Z_POSITION
        
        // Apply tint using SpriteKit's color properties
        symbolSprite?.color = color
        symbolSprite?.colorBlendFactor = 1.0  // Full tint
        
        if let sprite = symbolSprite {
            addChild(sprite)
            print("🔍 Symbol sprite added with size: \(sprite.size), color: \(sprite.color), blendFactor: \(sprite.colorBlendFactor)")
        } else {
            print("❌ Failed to create symbol sprite")
        }
    }
    
    private func setupSFSymbol(symbolName: String, color: SKColor, size: CGFloat? = nil) {
        symbolSprite?.removeFromParent()
        
        let symbolSize = size ?? (frame.width * MenuButtonNode.DEFAULT_SYMBOL_SIZE_RATIO)
        let configuration = UIImage.SymbolConfiguration(pointSize: symbolSize, weight: SYMBOL_WEIGHT)
        
        if let symbolImage = UIImage(systemName: symbolName, withConfiguration: configuration) {
            #if os(OSX)
            let uiColor = color as NSColor
            #else
            let uiColor = color as UIColor
            #endif
            
            let renderer = UIGraphicsImageRenderer(size: symbolImage.size)
            let coloredImage = renderer.image { context in
                uiColor.setFill()
                symbolImage.withTintColor(uiColor, renderingMode: .alwaysTemplate).draw(at: .zero)
            }
            
            let texture = SKTexture(image: coloredImage)
            symbolSprite = SKSpriteNode(texture: texture)
            symbolSprite?.position = CGPoint.zero
            symbolSprite?.zPosition = MenuButtonNode.SYMBOL_Z_POSITION
            
            if let sprite = symbolSprite {
                addChild(sprite)
            }
        }
    }
    
    private func getEffectiveSymbolColor(from color: SKColor, blendFactor: CGFloat, alpha: CGFloat) -> SKColor {
        print("🎨 Color info - BlendFactor: \(blendFactor), Alpha: \(alpha)")
        print("🎨 Sprite color: \(color)")
        
        // Use sprite's color properties from attributes inspector
        if blendFactor > 0 {
            // Use the sprite's tint color with the sprite's alpha
            #if os(OSX)
            let finalColor = NSColor(red: color.redComponent, green: color.greenComponent, blue: color.blueComponent, alpha: alpha)
            print("🎨 macOS final color: R:\(color.redComponent) G:\(color.greenComponent) B:\(color.blueComponent) A:\(alpha)")
            return finalColor
            #else
            var r: CGFloat = 0, g: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
            color.getRed(&r, green: &g, blue: &b, alpha: &a)
            let finalColor = UIColor(red: r, green: g, blue: b, alpha: alpha)
            print("🎨 iOS final color: R:\(r) G:\(g) B:\(b) A:\(alpha)")
            return finalColor
            #endif
        } else {
            // No tint color, use default white with sprite's alpha
            print("🎨 Using default white color")
            #if os(OSX)
            return NSColor(red: MenuButtonNode.DEFAULT_SYMBOL_COLOR_R, green: MenuButtonNode.DEFAULT_SYMBOL_COLOR_G, blue: MenuButtonNode.DEFAULT_SYMBOL_COLOR_B, alpha: alpha)
            #else
            return UIColor(red: MenuButtonNode.DEFAULT_SYMBOL_COLOR_R, green: MenuButtonNode.DEFAULT_SYMBOL_COLOR_G, blue: MenuButtonNode.DEFAULT_SYMBOL_COLOR_B, alpha: alpha)
            #endif
        }
    }
    
    private func getEffectiveSymbolColor() -> SKColor {
        return getEffectiveSymbolColor(from: self.color, blendFactor: self.colorBlendFactor, alpha: self.alpha)
    }
    
    // THese get called form the foundation class.  These are overridden in the buttons
    func onButtonDown(){
        print("🔄 onButtonDown() called for \(name ?? "unnamed") - useToggle: \(useToggle)")
        // Handle toggle functionality
        if useToggle {
            print("🔄 Calling toggleState()")
            toggleState()
        }
    }
    func onButtonUp(){}
    
    func onActivate(){}
    func onDeactivate(){}
    
    #if os(OSX)
    
    @objc func onMouseEnter(){ }
    @objc func onMouseExit(){ }

    
    /// Mouse-based event handling
    override func mouseDown(with event: NSEvent) {
        theUIButtonDelegate?.onButtonDown();
    }
    override func mouseMoved(with event: NSEvent) {
    }
    
    override func mouseUp(with event: NSEvent) {
        theUIButtonDelegate?.onButtonUp();
    }
    
    
    #endif
    
// MARK: - iOS
    #if os(iOS)
    /// Determine if any of the touches are within the `ButtonNode`.
    private func containsTouches(touches: Set<UITouch>) -> Bool {
        guard let scene = scene else { fatalError("Button must be used within a scene.") }
        
        return touches.contains { touch in
            let touchPoint = touch.location(in: scene)
            let touchedNode = scene.atPoint(touchPoint)
            return touchedNode === self || touchedNode.inParentHierarchy(self)
        }
    }
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesMoved(touches, with: event)
        //if containsTouches(touches: touches) {
            theUIButtonDelegate?.onButtonMoved?(touches, with: event)
        //}
        
        
    }
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        if containsTouches(touches: touches) {
            colorTouchStart()
//            HapticManager.shared.buttonPressed()

            // Handle toggle functionality here (before delegate calls)
            if useToggle {
                print("🔄 Touch began - calling toggleState()")
                toggleState()
            }

            theUIButtonDelegate?.onButtonDown?(touches, with: event)
            theUIButtonDelegate?.onButtonDown()
            isPressed = true
        }
    }
    
    func colorTouchStart(){
        if circleShape != nil {
            // Only color the circle, leave the symbol unchanged - use semi-transparent highlight
            #if os(OSX)
            let highlightColor = NSColor(red: MenuButtonNode.HIGHLIGHT_COLOR_R, green: MenuButtonNode.HIGHLIGHT_COLOR_G, blue: MenuButtonNode.HIGHLIGHT_COLOR_B, alpha: MenuButtonNode.HIGHLIGHT_COLOR_A)
            #else
            let highlightColor = UIColor(red: MenuButtonNode.HIGHLIGHT_COLOR_R, green: MenuButtonNode.HIGHLIGHT_COLOR_G, blue: MenuButtonNode.HIGHLIGHT_COLOR_B, alpha: MenuButtonNode.HIGHLIGHT_COLOR_A)
            #endif
            circleShape?.fillColor = highlightColor
        } else {
            // Regular sprite behavior
            #if os(OSX)
            let clr = SKAction.colorize(with: NSColor(red: MenuButtonNode.HIGHLIGHT_COLOR_R, green: MenuButtonNode.HIGHLIGHT_COLOR_G, blue: MenuButtonNode.HIGHLIGHT_COLOR_B, alpha: 1.0), colorBlendFactor: 1.0, duration: 0.0)
            #else
            let clr = SKAction.colorize(with: UIColor(red: MenuButtonNode.HIGHLIGHT_COLOR_R, green: MenuButtonNode.HIGHLIGHT_COLOR_G, blue: MenuButtonNode.HIGHLIGHT_COLOR_B, alpha: 1.0), colorBlendFactor: 1.0, duration: 0.0)
            #endif
            self.run(clr)
        }
    }
    
    func colorTouchEnd(){
        if circleShape != nil {
            // Reset circle to original color, leave symbol unchanged
            circleShape?.fillColor = originalCircleColor
        } else {
            // Regular sprite behavior
            #if os(OSX)
            let clr = SKAction.colorize(with: NSColor(red: MenuButtonNode.RESET_COLOR_R, green: MenuButtonNode.RESET_COLOR_G, blue: MenuButtonNode.RESET_COLOR_B, alpha: 1.0), colorBlendFactor: 0.0, duration: 0.0)
            #else
            let clr = SKAction.colorize(with: UIColor(red: MenuButtonNode.RESET_COLOR_R, green: MenuButtonNode.RESET_COLOR_G, blue: MenuButtonNode.RESET_COLOR_B, alpha: 1.0), colorBlendFactor: 0.0, duration: 0.0)
            #endif
            self.run(clr)
        }
    }
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        if containsTouches(touches: touches) {
//            HapticManager.shared.buttonReleased()
            theUIButtonDelegate?.onButtonUp?(touches, with: event)
            theUIButtonDelegate?.onButtonUp();
            colorTouchEnd()
            isPressed = false
        }else{
            if isVirtualButton{
                theUIButtonDelegate?.onButtonUp?(touches, with: event)
                theUIButtonDelegate?.onButtonUp();
            }
            resetcolor()
        }
    }
    
    
    override func touchesCancelled(_ touches: Set<UITouch>?, with event: UIEvent?) {
        super.touchesCancelled(touches!, with: event)
    }
    
    
    #endif
    
    func colorme(){
        //let it = SKAction.scale(to: 1.25, duration: 0.15)
        #if os(OSX)
        let clr = SKAction.colorize(with: NSColor.systemGreen, colorBlendFactor: 1.0, duration: 0.15)
        
        #else
        let clr = SKAction.colorize(with: UIColor.systemGreen,colorBlendFactor: 1.0, duration: 0.15)
        #endif
        
        let group = SKAction.group([/*it,*/clr])
        
        if let circleShape = circleShape {
            circleShape.run(group)
        } else {
            self.run(group)
        }
    }
    
    func resetcolor(){
        //let resetScale = SKAction.scale(to: 1.0, duration: 0.15)
        
        let targetNode = circleShape ?? self
        let targetColor: SKColor
        
        if circleShape != nil {
            // Reset to original circle color
            targetColor = originalCircleColor
        } else {
            // Reset to white for regular sprite
            #if os(OSX)
            targetColor = NSColor.white
            #else
            targetColor = UIColor.white
            #endif
        }
        
        let clr = SKAction.colorize(with: targetColor, colorBlendFactor: 1.0, duration: 0.15)
        let group = SKAction.group([/*resetScale,*/clr])
        
        targetNode.run(group, completion: {
            if self.circleShape != nil {
                self.circleShape?.fillColor = self.originalCircleColor
            } else {
                #if os(OSX)
                self.color = NSColor.white
                #else
                self.color = UIColor.white
                #endif
            }
        })
        self.active = false
    }
}
