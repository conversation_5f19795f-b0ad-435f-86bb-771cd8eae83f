//
//  SettingsScene.swift
//  De<PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit
import GameKit

class LogoBumperScene: FoundationScene {
    

    override func sceneDidLoad() {
        scaleMode = .aspectFit
        GameState.currentState = .bumper1
        super.sceneDidLoad()
        GKAccessPoint.shared.isActive = false
        #if os(tvOS)
            SceneManager.shared.vc?.controllerUserInteractionEnabled = false
        #endif
        UserPrefs.init()
    }
    
    override func didMove(to view: SKView) {
        if let logo = childNode(withName: "logo"){
            let fadein = SKAction.fadeIn(withDuration:Constants.kFADE_IN_DURATION)
            let pause = SKAction.wait(forDuration: Constants.kTRANSITION_PAUSE_DURATION)
            let fadeout = SKAction.fadeOut(withDuration: Constants.kFADE_OUT_DURATION)
            let seq = SKAction.sequence([fadein,pause,fadeout])
            logo.run(seq) {
//            SceneManager.shared.gotoScene("TaaraBumper")
                SceneManager.shared.gotoScene("MenuScene")
            }
        }
    }
    
}
