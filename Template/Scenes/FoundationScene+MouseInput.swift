//
//  FounsationScene+MouseInput.swift
//  Template
//
//  Created by <PERSON> on 4/19/21.
//

import Foundation
import SpriteKit
import GameKit
extension FoundationScene {
	
	#if os(OSX)
	
	/// KEEP THIS HERE FOR FUTURE USE!!!
	//  Mouse-based event handling
	//		override func mouseDown(with event: NSEvent) {
	//			//self.scene?.mouseDown(with: event)
	//			print("foundation scene:onButtonUp")
	//		btn_a = false;
	//		theMousenputDelegate.buttonPressedMouse(controllerItem.btn_a)
	//	}
		override func mouseMoved(with event: NSEvent) {}
	//	override func mouseUp(with event: NSEvent) {
	//		btn_b = false;
	//		theMousenputDelegate.buttonReleasedMouse(controllerItem.btn_b)
	//	}
	//
	#endif
	
}
