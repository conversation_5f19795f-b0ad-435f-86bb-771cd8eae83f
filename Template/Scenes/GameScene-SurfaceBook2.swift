//
//  GameScene.swift
//  <PERSON><PERSON>s Field Shared
//
//  Created by <PERSON> on 2/26/21.
//

import SpriteKit
import GameKit


// MAKE THE GAME SCENE A CHILD OD THIS SCENE???
class GameScene: FoundationScene, SKPhysicsContactDelegate{
	
	
	var transitionDone = false;
	
	var rockEngine:Rocks?
	var guns:Guns?
	var shoot = false
	
	fileprivate var thrustKey = false
	
	var rotateKeyLeft:CGFloat = 0.0
	var rotateKeyRight:CGFloat = 0.0
	
	var overlay:SKSpriteNode?
	
	var gamePaused = false
	
	var scoreText:ScoreText?
	var localHighScoreText:ScoreText?
	
	func setUpScene() {
		
		Analytics.shared.sendEvent("play")
		
		
		GameData.stage = self
		GameData.stageWidth = self.size.width
		GameData.stageHeight = self.size.height
		
		GameData.hero = Ship()
		
		rockEngine = Rocks()
		guns = Guns();
		
		//        guns?.isMachineGun = true
		//        guns?.isTripleShot = true
		/* rockEngine?.launchRock(GameData.kROCK_SML, x:CGFloat.random(0, upper: GameData.stageWidth!),y:CGFloat.random(0, upper: GameData.stageWidth!))
		}
		
		for _ in 1...10{
		rockEngine?.launchRock(GameData.kROCK_MED, x:CGFloat.random(0, upper: GameData.stageWidth!),y:CGFloat.random(0, upper: GameData.stageWidth!))
		}
		*/
		
		//        for _ in 1...5{
		//			let px = CGFloat.random(0, upper: self.frame.width)
		//            rockEngine?.launchRock(GameData.kROCK_BIG, x:px,y:0)
		//        }
		
		
		
		//        NotificationCenter.default.addObserver(self, selector: #selector(launchAnotherRock(_:)), name: NSNotification.Name(rawValue: "launchAnotherRock"), object: nil)
		
		
		
		
		
	}
	
	@objc func launchAnotherRock(_ notification: Notification){
		
		
		let thesize = notification.userInfo!["size"] as! Int
		let nx = notification.userInfo!["x"] as! CGFloat
		let ny = notification.userInfo!["y"] as! CGFloat
		let howMany = notification.userInfo!["rockCount"] as! Int
		// let theScore = notification.userInfo!["score"] as! Int
		
		//        GameData.score += theScore
		//        scoreBitmap?.drawScore(String(GameData.score))
		//
		//        launchExplosion(15, x: nx, y: ny)
		
		for _ in 1...howMany{
			switch thesize{
			case GameData.kROCK_BIG:
				rockEngine?.launchRock(GameData.kROCK_BIG,x:nx,y:ny)
				break
			case GameData.kROCK_MED:
				
				
				rockEngine?.launchRock(GameData.kROCK_MED,x:nx,y:ny)
				break
			case GameData.kROCK_SML:
				rockEngine?.launchRock(GameData.kROCK_SML,x:nx,y:ny)
				break
			case GameData.kNewRockAndOrBonus:
				//a new rock
				// big rock dies.  bir rick lives
				//rockEngine?.launchRock(GameData.kROCK_BIG,x:CGFloat.random(0, upper: GameData.stageWidth!),y:0)
				
				// bonusEngine?.launchRock(2, x: nx, y: ny)
				
				
				// 45% of getting abonus item of some kind
				if (Int.chanceRoll(45) == true){
					// launchBonus(nx, y: ny)
				}
				
				//// ##% change of getting some juice
				if (Int.chanceRoll(33) == true){
					//launchBonusBullets(Int.random(0, upper: 3),x:nx, y: ny)
				}
				break
				
			default:
				break
				
			}//end switch
		}//end for loop
		
	}
	
	override func sceneDidLoad() {
		scaleMode = .aspectFit
		super.sceneDidLoad()
		#if os(tvOS)
		SceneManager.shared.vc?.controllerUserInteractionEnabled = false
		#endif
		
		//GKAccessPoint.shared.isActive = false
		
		physicsWorld.gravity = CGVector(dx: 0,dy: 0);
		physicsWorld.contactDelegate = self;
		
		GameState.currentState = GameState.states.playing
		
		scoreText = ScoreText(withScene: self, position:CGPoint(x:100.0,y:self.size.height-100))
		localHighScoreText = ScoreText(withScene: self, position:CGPoint(x:100.0,y:self.size.height-150), scale: 0.45)
		//		localHighScoreText?.setScale(0.35)
		
		self.childNode(withName: "btnBack")?.isHidden = true
		
	}
	
	
	override func didMove(to view: SKView) {
		super.didMove(to: view)
		//		DispatchQueue.main.asyncAfter(deadline: .now() + 1.0){}
		
		
	}
	
	//	@objc override func controllerDisconnected(_ notification:Notification) {
	//			super.controllerDisconnected(notification)
	//			pauseGame()
	//
	//		#if os(iOS)
	//		virtualButtons?.forEach({ (theBtn) in
	//									theBtn.isHidden = true
	//
	//		})
	//		#endif
	//	}
	//
	//	@objc override func controllerDidConnect(_ notification:Notification) {
	//		super.controllerDidConnect(notification)
	//
	//		#if os(iOS)
	//		virtualButtons?.forEach({ (theBtn) in
	//			theBtn.isHidden = false
	//
	//		})
	//		#endif
	//	}
	override func transitionComplete() {
		//super.transitionComplete()
		physicsWorld.gravity = CGVector(dx: 0,dy: 0);
		physicsWorld.contactDelegate = self;
		self.setUpScene()
		
		print("transitionComplete gamescene")
		theControllerInputDelegate = self
		self.transitionDone = true
		if (GameData.controllerConnected){
			//self.hideVirtualButtons()
		}
	}
	
	
	
	override func willMove(from view: SKView) {
		super.willMove(from: view)
		guns = nil
		rockEngine = nil
		GameData.hero = nil;
		//		theTransitionDelegate?.transitionComplete()
	}
	
	override func update(_ currentTime: TimeInterval) {
		// Called before each frame is rendered
		if transitionDone == false  {
			return
		}
		
		switch GameState.currentState {
		case .playing:
			runGame()
		case .gameover:
			rockEngine?.wrapAllRocks()
		case .paused:
			break;
			
		default:
			runGame()
		//
		//		case .menu:
		//
		//		case .nextlevel:
		//
		//		case .paused:
		//
		//		case .betweenlevels:
		
		}
		
	}
	
	func runGame(){
		GameData.hero!.update()
		if (thrustKey){
			GameData.hero.thrust()
		}//else{
		// don't need this.  I set the linearDamping of ship to .2 or greater for drag
		// GameData.hero.drag()
		//}
		if (rotateKeyLeft != 0.0){
			GameData.hero.rotate(GameData.rotSpeed * rotateKeyLeft)
		}
		if (rotateKeyRight != 0.0){
			GameData.hero.rotate(GameData.rotSpeed * rotateKeyRight)
		}
		
		guns?.shoot();
		
		rockEngine?.wrapAllRocks()
		
	}
	//MARK:
	//MARK: PHYSICS
	
	func didBegin(_ contact: SKPhysicsContact) {
		
		
		
		//this gets called automatically when two objects begin contact with each other
		
		let contactMask = contact.bodyA.categoryBitMask | contact.bodyB.categoryBitMask
		
		switch(contactMask) {
		
		case (PhysicsCollisionMasks.rock | PhysicsCollisionMasks.bullet):
			//either the contactMask was the bro type or the ground type
			
			if (contact.bodyA.categoryBitMask == PhysicsCollisionMasks.bullet){
				if (contact.bodyA.node != nil){
					let thenode = contact.bodyA.node! as! Bullet
					thenode.kill()
				}
			}else if (contact.bodyB.categoryBitMask == PhysicsCollisionMasks.bullet){
				if (contact.bodyB.node != nil){
					let thenode = contact.bodyB.node! as! Bullet
					thenode.kill()
				}
			}
			
			
			
			if (contact.bodyA.categoryBitMask == PhysicsCollisionMasks.rock){
				if (contact.bodyA.node != nil){
					let thenode = contact.bodyA.node! as! Rock
					thenode.hit()
				}
			}else if (contact.bodyB.categoryBitMask == PhysicsCollisionMasks.rock){
				if (contact.bodyB.node != nil){
					let thenode = contact.bodyB.node! as! Rock
					thenode.hit()
				}
			}
			
			break
			
		// SHIP COLLIDES WITH BONUS ITEM
		case (PhysicsCollisionMasks.hero | PhysicsCollisionMasks.bonus):
			if (contact.bodyA.categoryBitMask == PhysicsCollisionMasks.bonus){
				if (contact.bodyA.node != nil){
					// let thenode = contact.bodyA.node! as! BonusItem
					//  thenode.hit()
					
				}
			}else if (contact.bodyB.categoryBitMask == PhysicsCollisionMasks.bonus){
				if (contact.bodyB.node != nil){
					// let thenode = contact.bodyB.node! as! BonusItem
					// thenode.hit()
				}
			}
			
			
			break
			
		case (PhysicsCollisionMasks.rock | PhysicsCollisionMasks.hero):
			//print("FUCK")
			
			if (GameState.currentState == .gameover){
				return
			}
			
			//DEBUG
			
			
			GameState.currentState = .gameover
			gameOver()
			
			
			//
			
			//            var theSize:Int = 0
			//
			//            if (contact.bodyA.categoryBitMask == PhysicsCollisionMasks.rock){
			//
			//
			//
			//                if (contact.bodyA.node != nil){
			//                    let thenode = contact.bodyA.node! as! Rock
			//                    theSize = thenode.mySize!
			//                    thenode.hit()
			//                }
			//                if (contact.bodyB.node != nil){
			//                    let thenode = contact.bodyB.node!.parent as! Ship
			//                    thenode.hit(theSize)
			//                }
			//            }else if (contact.bodyA.categoryBitMask == PhysicsCollisionMasks.hero){
			//                if (contact.bodyB.node != nil){
			//                    let thenode = contact.bodyB.node! as! Rock
			//                    theSize = thenode.mySize!
			//                    thenode.hit()
			//                }
			//                if (contact.bodyA.node != nil){
			//                    let thenode = contact.bodyA.node!.parent as! Ship
			//                 //   self.updateHealth(-0.25)
			//                    thenode.hit(theSize)
			//                }
			//            }
			//
			//            GameData.hero!.kill()
			break
		default:
			return
			
		}
		
	}
	
	
	func didEnd(_ contact: SKPhysicsContact) {
		
		
		//this gets called automatically when two objects end contact with each other
		
		//   let contactMask = contact.bodyA.categoryBitMask | contact.bodyB.categoryBitMask
		
		/*  switch(contactMask) {
		case BodyType.bro.rawValue | BodyType.ground.rawValue:
		//either the contactMask was the bro type or the ground type
		println("contact ended")
		
		default:
		return
		
		}
		*/
	}
	
	func gameOver(){
		
		// GKAccessPoint.shared.isActive = true
		
		self.childNode(withName: "btnBack")?.isHidden = false
		
		if let go = childNode(withName: "gameover") as? SKSpriteNode {
			go.setScale(0.0)
			go.alpha = 0.0
			go.zPosition = GameData.zorder.gameover.rawValue
			
			let fade = SKAction.fadeIn(withDuration: 0.15)
			let grow = SKAction.scale(to: 1.0, duration: 0.15)
			let it = SKAction.group([fade,grow])
			go.run(it)
		}
	}
	
	func pauseGame(){
		//		isPaused = !isPaused;
		gamePaused = !gamePaused
		
		if (gamePaused){
			//show the paused grc
			if let pausedSprite = childNode(withName: "paused") as? SKSpriteNode {
				pausedSprite.alpha = 1.0
				pausedSprite.zPosition = 999
				GameState.currentState = .paused
				self.childNode(withName: "btnBack")?.isHidden = false
			}
			physicsWorld.speed = 0;
		}else{
			if let pausedSprite = childNode(withName: "paused") as? SKSpriteNode {
				pausedSprite.alpha = 0.0
			}
			physicsWorld.speed = 1.0
			GameState.currentState = .playing
			self.childNode(withName: "btnBack")?.isHidden = true
		}
	}
	
	override func buttonPressed(_ buttonName: String/*controllerItem*/,button:MenuButtonNode?) {
		//
		//		if GameData.theGamePad!.physicalInputProfile.buttons["Button A"]!.isPressed {
		//			if (shoot == false){
		//				self.guns?.onButtonDown()
		//				shoot = true
		//			}
		//		}
		
		switch buttonName {
		case "Direction Pad Left": //controllerItem.dpad_left:
			rotateKeyLeft = GameData.kCCW
			rotateKeyRight = 0.0
		case "Direction Pad Right": //controllerItem.dpad_right:
			rotateKeyRight = GameData.kCW
			rotateKeyLeft = 0.0
		case "Direction Pad Up", //controllerItem.dpad_up,
			 "Right Shoulder", //controllerItem.right_shoulder,
			 "Left Shoulder": // controllerItem.left_shoulder:
			thrustKey = true;
		case "Button A": //controllerItem.btn_a:
			if (shoot == false){
				self.guns?.onButtonDown()
				shoot = true
			}
		default: break
		}
	}
	
	override func buttonReleased(_ buttonName: String/*controllerItem*/,button:MenuButtonNode?) {
		
		switch buttonName {
		case "Direction Pad Left": //controllerItem.dpad_left:
			rotateKeyLeft = 0.0
		case "Direction Pad Right": //controllerItem.dpad_right:
			rotateKeyRight = 0.0
		case "Direction Pad Up", //controllerItem.dpad_up,
			 "Right Shoulder", //controllerItem.right_shoulder,
			 "Left Shoulder": //controllerItem.left_shoulder:
			thrustKey = false
		case "Button A": //controllerItem.btn_a:
			//if (shoot == true){
			self.guns?.onButtonUp()
			shoot = false
		//}
		case "Button B": //controllerItem.btn_b:
			if ((GameState.currentState == .gameover ) || (GameState.currentState == .paused)){
				//goBackToMenu()
				SceneManager.shared.gotoScene("MenuScene")				}
		case "Button Menu": //controllerItem.btn_menu:
			if (GameState.currentState == .gameover){
				//goBackToMenu()
				SceneManager.shared.gotoScene("MenuScene")
			}else{
				pauseGame()
			}
		default: break
		}
	}
	
	func onButtonDown(_ index: Int, name:String) {
	}
	func onButtonUp(_ index: Int, name:String) {
		switch name {
		case "btnPause":
			pauseGame()
		case "btnBack":
			//goBackToMenu()
			SceneManager.shared.gotoScene("MenuScene")
		default:
			break
		}
	}
	
	
}

