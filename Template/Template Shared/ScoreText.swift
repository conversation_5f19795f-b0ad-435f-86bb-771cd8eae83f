//
//  Numbers.swift
//  Template
//
//  Created by <PERSON> on 4/16/21.
//

import Foundation
import SpriteKit

final class ScoreText : SKNode{
	
	
	var score:Int = 0{
		
		didSet(theScoreToAdd){
			
			let scoreString = String(score)
			
			var n = 0;
			for c in scoreString {
				numberSprites[n].texture = SKTexture(imageNamed: "\(c)")
				numberSprites[n].isHidden = false
				n += 1
			}
			
		}
	}
	
	var savedScore:Int = 0
	
	let space:CGFloat = 5.0 //pixels
	
	var numberSprites:[SKSpriteNode] = [SKSpriteNode]()
	
	convenience init(withScene scene:SKScene, position pos:CGPoint, scale scl:CGFloat = 1.0) {
		self.init()
		
		scene.addChild(self)
		
		//savedScore = UserDefaults.standard.integer(forKey: "localHighScore")
		
	
		let scr = UserDefaults.standard.integer(forKey: "localHighScore")
		if scr != 0 {
			savedScore = scr
		}else{
			UserDefaults.standard.setValue(0, forKey: "localHighScore")
		}
		
		let startPos = pos
		
		// create the sprites
		for n in 0...7{ // 99,999,999 should be high enough for now.
			let s = SKSpriteNode(imageNamed: "0")
			s.position = startPos
			if (n > 0){
				s.position.x = self.numberSprites[n-1].position.x + self.numberSprites[n-1].size.width/2.0+s.size.width/2.0+self.space
				s.isHidden = true
			}
			self.numberSprites.append(s)
			
			s.setScale(scl)
			
			self.addChild(s)
		}
		
		//numberSprites.reverse()
		
	}
	override init() {
		super.init()
	}
	
	required init?(coder aDecoder: NSCoder) {
		super.init(coder: aDecoder)
	}


}
