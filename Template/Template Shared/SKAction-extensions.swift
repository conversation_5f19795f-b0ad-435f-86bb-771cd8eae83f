//
//  SKAction-extensions.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/8/21.
//

import SpriteKit
//import Foundation


public extension SKAction {
    // SHAKE EFFECT
    // USAGE:
    // let shake = SKAction.shake(node.position, duration: 0.3)
    // node.run(shake)
    //
    // or for a more intense shake you can configure the amplitude as well
    // let shake = SKAction.shake(node.position, duration: 0.3, amplitudeX: 12, amplitudeY: 3)
    // node.run(shake)
    
    class func shake(_ initialPosition:CGPoint, duration:Float, amplitudeX:Int = 12, amplitudeY:Int = 3) -> SKAction {
        let startingX = initialPosition.x
        let startingY = initialPosition.y
        let numberOfShakes = duration / 0.015
        var actionsArray:[SKAction] = []
        for _ in 1...Int(numberOfShakes) {
            let newXPos = startingX + CGFloat(arc4random_uniform(UInt32(amplitudeX))) - CGFloat(amplitudeX / 2)
            let newYPos = startingY + CGFloat(arc4random_uniform(UInt32(amplitudeY))) - CGFloat(amplitudeY / 2)
            actionsArray.append(SKAction.move(to: CGPoint(x: newXPos, y: newYPos), duration: 0.015))
        }
        actionsArray.append(SKAction.move(to: initialPosition, duration: 0.0))
        return SKAction.sequence(actionsArray)
    }
    
    class func selected(_ growSize:CGFloat, duration:TimeInterval) -> SKAction {
        
        let scaleUp = SKAction.scale(to: growSize, duration: duration);
        let scaleDown = SKAction.scale(to: 2.0, duration: duration);
        let seq = SKAction.sequence([scaleUp,scaleDown])
        return SKAction.repeatForever(seq)
        
    }
    
    class func flash(numberOfTimes times:Int) -> SKAction{
        let fi = SKAction.fadeIn(withDuration: 0.1)
        let fo = SKAction.fadeOut(withDuration: 0.1)
        let seq = SKAction.sequence([fo,fi])
        let rp = SKAction.repeat(seq, count: times)
        return rp
    }
}


