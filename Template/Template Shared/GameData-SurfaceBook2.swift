//
//  GameData.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 2/26/21.
//  Copyright © 2015 <PERSON>. All rights reserved.
//

import GameController
import SpriteKit


struct GameState{
	
	enum states:Int {
		case menu = 0, playing, nextlevel,paused,gameover,betweenlevels,settings,buttonPositions,help
	}
	
	static var currentState:states = .menu
	
	
}

struct PhysicsCollisionMasks {
    
    static let nothing:UInt32 = 0
    static let hero:UInt32 = 1 << 1
    static let bullet:UInt32 = 1 << 2
    static let rock:UInt32 = 1 << 4
    static let bonus:UInt32 = 1 << 8
    static let anotherBody3:UInt32 = 1 << 16
    static let flame:UInt32 = 1 << 32

}

public struct GameData{
    static var hero:Ship!
    
    static let kCW:CGFloat  = 1.0
    static let kCCW:CGFloat = -1.0
    static let rotSpeed:CGFloat = 0.07;
    
    static var stageWidth:CGFloat?
    static var stageHeight:CGFloat?
	
	static var farts:Bool = false
    
    static var stage:SKNode? //SKScene?

	static 	var theGamePad:GCController?
	static var theMouse:GCMouse?
	
	enum zorder:CGFloat{
		case rocks = 0.0,
			 ship = 5.0,
			 bonus = 10.0,
			 ui = 990.0,
			 vButtons = 998.0,
			 gameover = 999.0
		
	}
	
	static var rolloverNode:SKSpriteNode?
	
	static var controllerConnected = false;
	
    // BONUSES
    enum Bonus:Int{
        case machineGun = 1,
        trippleGun,
        longShot,
        shield,
        moreBullets,
        littleMoreBulleter,
        increaseHealth
    }
    
    enum bonuses:Int {
        case magnet = 1,
             invincible,
             freeze,
             slowmo ,
             bigjar,
             killallreds,
             machineGun,
             trippleGun,
             longShot,
             shield,
             moreBullets,
             littleMoreBullers,
             increaseHealth
    }
    
    
    
    var currentBonus = 0 //0
    var isMachineGun = false
    var isInvincible = false
    
    var isFrozen = false
    var isSlow = false
    
    
    
    var slowDownAmount:CGFloat = 1.0 // 1.0 is normal speed
    var bigJarBonus = false
    
    var shipSpeed:CGFloat = 0.0
    var speedLimit:CGFloat = 0.0
    
    
    let kBONUS_MAGNET = 1
    let kBONUS_FREEZE = 2
    
    var mainGameViewController:GameViewController!
    
    
    static var totalRocksOnScreen = 0;
    
    static var kNewRockAndOrBonus = 0
    static var kROCK_BIG = 1
    static var kROCK_MED = 2
    static var kROCK_SML = 3
    
    
    
}

//public let GameData = GameDataClass()

open class GameDataClass {
   
    
  
    
    var fragShader:SKShader?
    var effectNode : SKEffectNode?
    
    //MARK:
    //MARK: Controls
    var myController:GCController?
    var controllers:[GCController]?
    
    var remoteController:GCController?
    var gamePad1controller:GCController?
    var gamePad2controller:GCController?
    
    var dpadSensitivity:CGFloat = 5.0
    
    //MARK:
    //MARK: Game vars -
    var bugCount:Int = 0
    var heroProxy:SKNode!
    var hero:Ship!
    
    var speedMod:Int = 0
    var level = 1.0
    var lives = 3

    var totalRocksOnScreen = 0
    var totalBonusOnScreen = 0
    
    //MARK:
    //MARK: SOUND AND AUDIO
    var soundfx:Bool = false
    let SND_BUTTON = "button.mp3"
    var volume:Float = 0.0
    
    
   
    
    var score:Int = 0
    
  
    
    enum myScenes:Int {
        case sceneTitle = 1 ,sceneSelectA, sceneOptions, sceneHelp,sceneControls, sceneGame,sceneSaver
    }
    
    enum bonuses:Int {
        case magnet = 1, invincible, freeze, slowmo ,bigjar, killallreds
    }
    
    enum states:Int {
        case playing = 0, nextlevel,paused,gameover,betweenlevels
    }
    
    
    //MARK: -
    //MARK: physics vars -
    enum BodyCategory:UInt32 {
        case nothing = 0
        case hero = 1
        case bullet = 2
        case rock = 4
        case bonus = 8
        case anotherBody3 = 16
        case flame = 32        
    }


    
    var currentScene:myScenes?
    var gameState:states?
    
    var kNewRockAndOrBonus = 0
    var kROCK_BIG = 1
    var kROCK_MED = 2
    var kROCK_SML = 3
    
    
   
    
    var currentBonus = 0 //0
    var isMachineGun = false
    var isInvincible = false
    
    var isFrozen = false
    var isSlow = false
    
    
    
    var slowDownAmount:CGFloat = 1.0 // 1.0 is normal speed
    var bigJarBonus = false
    
    var shipSpeed:CGFloat = 0.0
    var speedLimit:CGFloat = 0.0
    
    
    let kBONUS_MAGNET = 1
    let kBONUS_FREEZE = 2
    
    var mainGameViewController:GameViewController!
    
    fileprivate init() {
    
//       volume = SDCloudUserDefaults.float(forKey: "volume")
//       soundfx =  SDCloudUserDefaults.bool(forKey: "soundfx")
//    
//        // CHECK FOR NaN!!!!!!!!
//        speedMod =  SDCloudUserDefaults.integer(forKey: "speedMod")
//        
//        if (speedMod == 0){
//            
//            speedMod = 5; //default
//            SDCloudUserDefaults.setInteger(5, forKey: "speedMod")
//            SDCloudUserDefaults.synchronize();
//            
//        }
        
   
    }
    
    func startRetroShader(_ theScene:SKScene){
        
     
        
//        let shader_move:SKShader = SKShader(fileNamed: "retro.fsh")
//        let vector = vector2(Float(1920.0), Float(1080))
//
//        shader_move.uniforms = [
//            SKUniform(name: "iChannel0", texture: SKTexture(imageNamed: "dummy2")),
////            SKUniform(name: "iResolution", float: GLKVector2Make(1920.0, 1080))
//            SKUniform(name: "size", vectorFloat2: vector)
// ]
//
//        theScene.shader = shader_move
//        theScene.shouldEnableEffects = true
        
        
        /* // shaderContainer_move.position = CGPointMake(self.frame.size.width/2, self.frame.size.height/2);
        // shaderContainer_move.size = CGSizeMake(self.frame.size.width, self.frame.size.height);
        // [self addChild:shaderContainer_move];
        //Create the shader from a shader-file
        //SKShader* shader_move = [SKShader shaderWithFileNamed:@"shader_water_movement.fsh"];
        //Set vairiables that are used in the shader script
        shader_move.uniforms = @[
        [SKUniform uniformWithName:@"size" floatVector3:GLKVector3Make(self.frame.size.width*1.5, self.frame.size.height*1.5, 0)],
        [SKUniform uniformWithName:@"customTexture" texture:[SKTexture textureWithImageNamed:@"sand.png"]],
        ];
        //add the shader to the sprite
        shaderContainer_move.shader = shader_move;
        */
        
        
    }

    
    // Etc...
}
