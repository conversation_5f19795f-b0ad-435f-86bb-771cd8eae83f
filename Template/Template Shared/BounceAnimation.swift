//
//  BounceAnimation.swift
//  Template
//
//  Created by <PERSON> on 7/26/25.
//

import SpriteKit

class BounceAnimationNode: SKNode {
    
    /// Animates a sprite to a new position and scale with a bounce effect
    /// - Parameters:
    ///   - sprite: The sprite to animate
    ///   - targetPosition: The final position
    ///   - targetScale: The final scale value
    ///   - duration: Total animation duration
    ///   - bounceIntensity: How much bounce (0.0 to 1.0, default 0.3)
    ///   - completion: Optional completion handler
    func animateSprite(_ sprite: SKSpriteNode,
                      to targetPosition: CGPoint,
                      scale targetScale: CGFloat,
                      duration: TimeInterval = 1.0,
                      bounceIntensity: CGFloat = 0.3,
                      completion: (() -> Void)? = nil) {
        
        // Remove any existing animations
        sprite.removeAllActions()
        
        // Create the movement animation with bounce
        let moveAction = createBounceMove(to: targetPosition, duration: duration, bounceIntensity: bounceIntensity)
        
        // Create the scale animation with bounce
        let scaleAction = createBounceScale(to: targetScale, duration: duration, bounceIntensity: bounceIntensity)
        
        // Run both animations simultaneously
        let groupAction = SKAction.group([moveAction, scaleAction])
        
        // Add completion if provided
        if let completion = completion {
            let sequence = SKAction.sequence([groupAction, SKAction.run(completion)])
            sprite.run(sequence)
        } else {
            sprite.run(groupAction)
        }
    }
    
    /// Creates a bounce movement animation
    private func createBounceMove(to targetPosition: CGPoint, duration: TimeInterval, bounceIntensity: CGFloat) -> SKAction {
        let moveAction = SKAction.move(to: targetPosition, duration: duration * 0.8)
        moveAction.timingMode = .easeOut
        
        // Create bounce effect
        let bounceDistance = bounceIntensity * 20.0
        let bounceBack = SKAction.moveBy(x: -bounceDistance * (targetPosition.x > 0 ? 1 : -1),
                                        y: -bounceDistance * (targetPosition.y > 0 ? 1 : -1),
                                        duration: duration * 0.1)
        bounceBack.timingMode = .easeOut
        
        let bounceForward = SKAction.moveBy(x: bounceDistance * (targetPosition.x > 0 ? 1 : -1),
                                           y: bounceDistance * (targetPosition.y > 0 ? 1 : -1),
                                           duration: duration * 0.1)
        bounceForward.timingMode = .easeIn
        
        return SKAction.sequence([moveAction, bounceBack, bounceForward])
    }
    
    /// Creates a bounce scale animation
    private func createBounceScale(to targetScale: CGFloat, duration: TimeInterval, bounceIntensity: CGFloat) -> SKAction {
        let mainScaleAction = SKAction.scale(to: targetScale, duration: duration * 0.8)
        mainScaleAction.timingMode = .easeOut
        
        // Create bounce effect for scale
        let overshoot = targetScale * (1.0 + bounceIntensity * 0.3)
        let bounceOverAction = SKAction.scale(to: overshoot, duration: duration * 0.1)
        bounceOverAction.timingMode = .easeOut
        
        let bounceBackAction = SKAction.scale(to: targetScale, duration: duration * 0.1)
        bounceBackAction.timingMode = .easeIn
        
        return SKAction.sequence([mainScaleAction, bounceOverAction, bounceBackAction])
    }
    
    /// Convenience method for common bounce animation
    func quickBounceAnimation(sprite: SKSpriteNode,
                            to position: CGPoint,
                            scale: CGFloat = 1.0,
                            completion: (() -> Void)? = nil) {
        animateSprite(sprite, to: position, scale: scale, duration: 0.6, bounceIntensity: 0.4, completion: completion)
    }
}

// MARK: - Usage Example
extension BounceAnimationNode {
    
    /// Example of how to use the bounce animation node
    func exampleUsage(in scene: SKScene) {
        // Create a test sprite
        let testSprite = SKSpriteNode(color: .red, size: CGSize(width: 50, height: 50))
        testSprite.position = CGPoint(x: 100, y: 100)
        scene.addChild(testSprite)
        
        // Animate to new position and scale with bounce
        animateSprite(testSprite,
                     to: CGPoint(x: 300, y: 400),
                     scale: 1.5,
                     duration: 1.2,
                     bounceIntensity: 0.4) {
            print("Animation completed with bounce!")
        }
        
        // Or use the quick method
        // quickBounceAnimation(sprite: testSprite, to: CGPoint(x: 300, y: 400), scale: 1.5)
    }
}

// MARK: - Alternative Implementation with Custom Timing Function
extension BounceAnimationNode {
    
    /// Creates a more sophisticated bounce using custom timing function
    func advancedBounceAnimation(sprite: SKSpriteNode,
                               to targetPosition: CGPoint,
                               scale targetScale: CGFloat,
                               duration: TimeInterval = 1.0) {
        
        sprite.removeAllActions()
        
        // Custom bounce timing function
        let bounceMove = SKAction.customAction(withDuration: duration) { node, elapsedTime in
            let progress = elapsedTime / CGFloat(duration)
            let bounceProgress = self.bounceEaseOut(progress)
            
            let startPos = CGPoint(x: 100, y: 100) // Store original position
            let newX = startPos.x + (targetPosition.x - startPos.x) * bounceProgress
            let newY = startPos.y + (targetPosition.y - startPos.y) * bounceProgress
            
            node.position = CGPoint(x: newX, y: newY)
        }
        
        let bounceScale = SKAction.customAction(withDuration: duration) { node, elapsedTime in
            let progress = elapsedTime / CGFloat(duration)
            let bounceProgress = self.bounceEaseOut(progress)
            let startScale: CGFloat = 1.0 // Store original scale
            let newScale = startScale + (targetScale - startScale) * bounceProgress
            
            node.setScale(newScale)
        }
        
        sprite.run(SKAction.group([bounceMove, bounceScale]))
    }
    
    /// Bounce easing function
    private func bounceEaseOut(_ t: CGFloat) -> CGFloat {
        if t < 4.0/11.0 {
            return (121.0 * t * t) / 16.0
        } else if t < 8.0/11.0 {
            return (363.0/40.0 * t * t) - (99.0/10.0 * t) + 17.0/5.0
        } else if t < 9.0/10.0 {
            return (4356.0/361.0 * t * t) - (35442.0/1805.0 * t) + 16061.0/1805.0
        } else {
            return (54.0/5.0 * t * t) - (513.0/25.0 * t) + 268.0/25.0
        }
    }
}
