//
//  GameCenter.swift
//  <PERSON><PERSON><PERSON> Field
//
//  Created by <PERSON> on 4/12/21.
//

import GameKit

final class GameCenter {
    
    static let shared = GameCenter()
    
    private init() {
//        let observation = GKAccessPoint.shared.observe(
//            \.frameInScreenCoordinates
//        ) { [weak self] _,_ in
//            let screenFrame = GKAccessPoint.shared.frameInScreenCoordinates
//            let accessPointFrame = SceneManager.shared.mainSKView?.convert(screenFrame, from: nil)
//            // adjust your layout
//            print(accessPointFrame)
//        }
        
        
    }
    
    private var localPlayer = GKLocalPlayer.local
    private let leadboardID = "com.markandrade.debrisfield.highscores" //"com.sputnikgames.aeroliteHS"
  //  private let leadboardID = "com.sputnikgames.aeroliteHS"
    
    private var leaderboard:GKLeaderboard?
    
    var isGameCenterEnabled = false;
    
    #if os(OSX)
        let highlightView: NSView = NSView(frame: NSMakeRect(0,0, 53,53))
        var win:NSWindow?
    #else
        let highlightView: UIView = UIView(frame: CGRect(x: 0.0, y: 0.0, width: 53.0, height: 53.0))
        //        var win:UIWindow?
    
    #endif
    
    #if os(iOS) || os(tvOS)
    func authenticateLocalPlayer(presentingVC: UIViewController) {
        // authentification method
        localPlayer.authenticateHandler = { [weak self] (gameCenterViewController, error) -> Void in
            // check if there are not error
            if error != nil {
                print(error!)
            } else if gameCenterViewController != nil {
                // 1. Show login if player is not logged in
                presentingVC.present(gameCenterViewController!, animated: true, completion: nil)
            } else if (self?.localPlayer.isAuthenticated ?? false) {
                // 2. Player is already authenticated & logged in, load game center
                self?.isGameCenterEnabled = true
            } else {
                // 3. Game center is not enabled on the users device
                self?.isGameCenterEnabled = false
                print("Local player could not be authenticated!")
            }
        }
    }
    #endif
    
    
    
    #if os(OSX)
    func authenticateLocalPlayer(presentingVC: NSViewController & GKViewController) {
        // authentification method
        
        
        localPlayer.authenticateHandler = { [weak self]  (gameCenterViewController, error) -> Void in
            // check if there are not error
            if error != nil {
                print(error!)
            } else if gameCenterViewController != nil {
                // 1. Show login if player is not logged in
                
                let gameCenterController = GKGameCenterViewController.init()
                
            
                GKDialogController.shared().parentWindow = NSApplication.shared.mainWindow
                GKDialogController.shared().present(gameCenterController)
                
            } else if (self!.localPlayer.isAuthenticated ) {
                // 2. Player is already authenticated & logged in, load game center
                self?.isGameCenterEnabled = true
            } else {
                // 3. Game center is not enabled on the users device
                self?.isGameCenterEnabled = false
                print("Local player could not be authenticated!")
            }
        }
    }
    
    
    func getGCWindow() -> NSWindow?{
        
        var win:NSWindow?
        
        NSApplication.shared.enumerateWindows(options: .orderedFrontToBack, using: {
            (window: NSWindow, stop: UnsafeMutablePointer<ObjCBool>) in
            
            if window.className.contains("AccessPointWindow") {
                stop.pointee = true
                win =  window
            }
            
        })
        
        return win
    }
    #else
    
    func getGCWindow() -> UIWindow?{
        
        var win:UIWindow?
        for _win in UIApplication.shared.windows{
            
            let it = String(describing: _win.classForCoder)
            if it.contains("AccessPointWindow") {
                
//                print("AccessPointWindow found")
//                print(_win.frame)
                win = _win
                break
            }
            
            
            //print(it,_win.frame)
        }
        return win
    }
    
    #endif
    
    func submitScore(_ score:Int) {
        // if player is logged in to GC, then report the score
        if GKLocalPlayer.local.isAuthenticated {
            
            Task{
                   try await GKLeaderboard.submitScore(
                       score,
                       context: 0,
                       player: GKLocalPlayer.local,
                       leaderboardIDs: [leadboardID]
                   )
            }
            
//            GKLeaderboard.submitScore(
//                score,
//                context: 0,
//                player: GKLocalPlayer.local,
//                leaderboardIDs: [leadboardID]
//            ) { error in
//                if (error != nil) {
//                    print(error!)
//                }
//            }
        }
        
    }
    
//    func getScores(){
//        if GKLocalPlayer.local.isAuthenticated {
//            GKLeaderboard
//
//        }
//    }
}
