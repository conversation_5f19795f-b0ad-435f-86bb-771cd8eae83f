//
//  AATools.swift
//  De<PERSON><PERSON> Field
//
//  Created by <PERSON> on 2/26/21.
//


import Foundation
import SpriteKit


public extension Int {
    /*
     Create a random num Int
     :param: lower number Int
     :param: upper number Int
     :return: random number Int
     By DaRkDOG
     */
    static func chanceRoll (_ percentChance:Int) -> Bool {
        let n =  Int.random(in: 0...100)
        //0 + Int(arc4random_uniform(UInt32(100 - 0 + 1)))
        if (n >= percentChance)
        {
            return false
        }
        else
        {
            return true
        }
        
    }
    
}

extension Int {
    var degreesToRadians : CGFloat {
        return CGFloat(self) * CGFloat(Float.pi) / 180.0
    }
}

// -->  45.degreesToRadians   // 0.785398163397448

extension CGFloat {
    var degreesToRadians : CGFloat {
        return CGFloat(self) * CGFloat(Float.pi) / 180.0
    }
}
extension CGFloat{
    var radiansToDegrees :CGFloat{
        return self * 180.0 / CGFloat(Float.pi)
    }
}
// 0.234.randiansToDegrees
extension CGFloat {
    var roundTo2f: CGFloat {return CGFloat((100.0  * self)/100.0).rounded()  }
    
    
    //    var roundTo2f: CGFloat {return CGFloat(round(100.0  * CGFloat.self)/100.0)  }
    var roundTo3f: CGFloat {return CGFloat((1000 * self)/1000).rounded() }
    
    @inlinable
    public func wrap( min:CGFloat, max:CGFloat) -> CGFloat
    {
        let range = max - min;
        let it = ( min + ((((self - min).truncatingRemainder(dividingBy: range) + range).truncatingRemainder(dividingBy:range))))
        return it
    }
    
    @inlinable
    public mutating func wrapit( min:CGFloat, max:CGFloat)
    {
        let range = max - min;
        self = ( min + ((((self - min).truncatingRemainder(dividingBy: range) + range).truncatingRemainder(dividingBy:range))))
    }
    
}
extension Double {
    var roundTo2f: Double {return Double((100  * self)/100).rounded()  }
    var roundTo3f: Double {return Double((1000 * self)/1000).rounded() }
    
    @inlinable
    public func wrap( min:Double, max:Double) -> Double
    {
        let range = max - min;
        let it = ( min + ((((self - min).truncatingRemainder(dividingBy: range) + range).truncatingRemainder(dividingBy:range))))
        return it
    }
    
}
//usage:
// let regularPie:  Double = 3.14159
// var smallerPie:  Double = regularPie.roundTo3f  // results 3.142
// var smallestPie: Double = regularPie.roundTo2f  // results 3.14


extension CGPoint {
    /**
     Calculates a distance to the given point.
     
     :param: point - the point to calculate a distance to
     
     :returns: distance between current and the given points
     */
    func distance(_ point: CGPoint) -> CGFloat {
        let dx = self.x - point.x
        let dy = self.y - point.y
        return sqrt(dx * dx + dy * dy);
    }
}

extension CGPoint {
    /**
     Calculates a distance to the given point.
     
     :param: point - the point to calculate a distance to
     
     :returns: distance between current and the given points
     */
    func angleBetween(_ point: CGPoint) -> CGFloat {
        //let dx = self.x - point.x
        //let dy = self.y - point.y
        return atan2(point.y - self.y, point.x - self.x); //sqrt(dx * dx + dy * dy);
    }
}
// node.posiition.angleBetween(anotherNode.position)



extension SKNode{
    func screenWrap(width:CGFloat,height:CGFloat) {
        // based on 0,0 being in the center of the screen
        // Requires wrap() extension!
        
        self.position.x = self.position.x.wrap(min: 0, max: width)
        self.position.y = self.position.y.wrap(min: 0, max: height)
    }
}

