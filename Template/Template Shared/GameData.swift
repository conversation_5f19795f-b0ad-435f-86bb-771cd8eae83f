//
//  GameData.swift
//  De<PERSON><PERSON> Field
//
//  Created by <PERSON> on 2/26/21.
//  Copyright © 2015 <PERSON>. All rights reserved.
//

import GameController
import SpriteKit


struct GameState{
    
    enum states:Int {
        case menu = 0, playing, nextlevel,paused,gameover,betweenlevels,levelWait,levelCountDown,counting,eolCountComplete,settings,buttonPositions,help,bumper1,bumper2,loadPurchase,initialLoad
    }
    
    static var currentState:states = .menu
    
}

struct PhysicsCollisionMasks {
    
    static let nothing:UInt32 = 0
    static let hero:UInt32 = 1 << 1
    static let bullet:UInt32 = 1 << 2
    static let darkMatter:UInt32 = 1 << 3
    static let rock:UInt32 = 1 << 4
    static let shield:UInt32 = 1 << 5
    static let multiplier:UInt32 = 1 << 7
    static let bonusItem:UInt32 = 1 << 8
    static let lostPerson:UInt32 = 1 << 9
    static let missle:UInt32 = 1 << 10
    static let anotherBody3:UInt32 = 1 << 16
    static let flame:UInt32 = 1 << 32

}

struct itemAppearsAtLevel{
    
    static let superNova = 1;
    static let blackHole = 1;
    static let ironAsteroid = 5;
    static let missle = 5;
    static let rescue = 5;
    
}

struct percentChanceOfLaunching{
    
    static let bonusItem  = 45;
    static let multiplier = 25
    static let resuce     = 30;
    static let superNova  = 15;
    static let darkMatter = 30;
    static let blackHole  = 10;
    static let missle     = 33;
}

public struct GameData{
    
    
    static let kLEADERBOARD_ID = "com.markandrade.debrisfield.highscores"
    //com.sputnikgames.aerolitefreeHS"
    
    static var hero:Ship!
    
//    static var score:Int = 0;
    
    static let kCW:CGFloat  = 1.0
    static let kCCW:CGFloat = -1.0
    static let rotSpeed:CGFloat = 0.07;
    
    static var stageWidth:CGFloat?
    static var stageHeight:CGFloat?
    
    static var farts:Bool = false
    
    static var stage:SKNode? //SKScene?
    static var stageNode:SKNode?
    
    static     var theGamePad:GCController?
    static var theMouse:GCMouse?
    
    static var dispatchGroup = DispatchGroup()
    
    static let kIRON_ROCK_HITS = 500
    
    enum zorder:CGFloat{
        case rocks = 0.0,
             ship = 5.0,
             shield = 6.0,
             bonus = 10.0,
             ui = 990.0,
             vButtons = 998.0,
             gameover = 999.0
        
    }
    
    static var rolloverNode:SKSpriteNode?
    
    static var controllerConnected = false;
    
    // BONUSES
    enum BonusItems:Int,CaseIterable{
        case machineGun = 1,
            trippleGun,
            longShot,
            shield,
            drag,
            freeze,
            bulletWrap
    }
    static var bulletLifeSpan = 30.0
    static var bonusLifeSpan = 1.0
    static var bonusLifeSpanIncrease = 2.1
    
    static var warpBullets = false;
    
   // var slowDownAmount:CGFloat = 1.0 // 1.0 is normal speed

    var shipSpeed:CGFloat = 0.0
    var speedLimit:CGFloat = 0.0
    
    
//    let kBONUS_MAGNET = 1
//    let kBONUS_FREEZE = 2
    
   // static var mainGameViewController:GameViewController!
    
    
    static var totalRocksOnScreen = 0;
    
    static var kNewRockAndOrBonus = 0
    static var kROCK_BIG = 1
    static var kROCK_MED = 2
    static var kROCK_SML = 3
    
    static var blackHoleRunning:Bool = false;
    static var superNovaRunning:Bool = false;
    
    static var sound_shoot = SKAction.playSoundFileNamed("shoot.wav", waitForCompletion: false)
    static var sound_bonusCaught = SKAction.playSoundFileNamed("bonusCaught.wav", waitForCompletion: false)
    static var sound_bonusLaunch = SKAction.playSoundFileNamed("bonusLaunch.wav", waitForCompletion: false)
    static var sound_bonusMissed = SKAction.playSoundFileNamed("bonusMissed.wav", waitForCompletion: false)
    static var sound_shipExplode = SKAction.playSoundFileNamed("shipExplode.wav", waitForCompletion: false)
    static var sound_rockExpodeBig = SKAction.playSoundFileNamed("explosion.wav", waitForCompletion: false)
    static var sound_rockExpode = SKAction.playSoundFileNamed("explosion2.wav", waitForCompletion: false)
    static var sound_noSheild = SKAction.playSoundFileNamed("noShield.wav", waitForCompletion: false)
    
    static var sound_supernove = SKAction.playSoundFileNamed("supernove.mp3", waitForCompletion: false)
    static var sound_blackhole = SKAction.playSoundFileNamed("blackhole.mp3", waitForCompletion: false)
    static var sound_missle = SKAction.playSoundFileNamed("missle.mp3", waitForCompletion: false)
    static var sound_rescue = SKAction.playSoundFileNamed("rescue.mp3", waitForCompletion: false)
    static var sound_rescueSaved = SKAction.playSoundFileNamed("rescue.wav", waitForCompletion: false)
    //mutiplier
    static var sound_mutiplierLaunch = SKAction.playSoundFileNamed("mutiplierLaunch.wav", waitForCompletion: false)
    static var sound_mutiplierMissed = SKAction.playSoundFileNamed("mutiplierMissed.wav", waitForCompletion: false)
    static var sound_mutiplierCaught = SKAction.playSoundFileNamed("mutiplierCaught.wav", waitForCompletion: false)
    static var sound_blip = SKAction.playSoundFileNamed("blip.wav", waitForCompletion: false)
    
    static var sound_buttonBack = SKAction.playSoundFileNamed("sfxButton_back.mp3", waitForCompletion: false)
    static var sound_buttonPlay = SKAction.playSoundFileNamed("sfxButton_play.mp3", waitForCompletion: false)
    static var sound_buttonSelect = SKAction.playSoundFileNamed("sfxButton_select.mp3", waitForCompletion: false)

    static var sound_levelText = SKAction.playSoundFileNamed("levelText.wav", waitForCompletion: false)

    static var sound_badShot = SKAction.playSoundFileNamed("badshot.wav", waitForCompletion: false)
    
    //static var sound_thrust = SKAction.playSoundFileNamed("thrust2.wav", waitForCompletion: false)
    static var sound_thrust = SKAudioNode(fileNamed: "thrust.wav")
    
    static func playSound(_ theSound:SKAction){
        if (UserPrefs.soundOn == true){
            SceneManager.shared.currentScene?.run(theSound);
        }
    }
    
    static func showAlert(_ title:String, theMessage:String,todo:@escaping () -> Void){
        
        #if !os(OSX)
        let dialogMessage = UIAlertController(title: title, message: theMessage, preferredStyle: .alert)
        
        // Create OK button with action handler
        let ok = UIAlertAction(title: "OK", style: .default, handler: { (action) -> Void in
            //print("Ok button tapped")
            //SceneManager.shared.gotoScene("MenuScene")
            todo()
            
         })
        
        //Add OK button to a dialog message
        dialogMessage.addAction(ok)
        // Present Alert to
        SceneManager.shared.vc?.present(dialogMessage, animated: true, completion: nil)
        
        #endif
        
        #if os(OSX)
            let alert = NSAlert()
            alert.messageText = theMessage
            alert.informativeText = title
            alert.alertStyle = .warning
            alert.addButton(withTitle: "OK")
        
            if alert.runModal() == .alertFirstButtonReturn {
                todo()
            }
   
        
        #endif
    }
}

//public let GameData = GameDataClass()

//open class GameDataClass {
//
//    var fragShader:SKShader?
//    var effectNode : SKEffectNode?
//
//    //MARK:
//    //MARK: Controls
//    var myController:GCController?
//    var controllers:[GCController]?
//
//    var remoteController:GCController?
//    var gamePad1controller:GCController?
//    var gamePad2controller:GCController?
//
//    var dpadSensitivity:CGFloat = 5.0
//
//    //MARK:
//    //MARK: Game vars -
//    var bugCount:Int = 0
//    var heroProxy:SKNode!
//    var hero:Ship!
//
//    var speedMod:Int = 0
//    var level = 1.0
//    var lives = 3
//
//    var totalRocksOnScreen = 0
//    var totalBonusOnScreen = 0
//
//    //MARK:
//    //MARK: SOUND AND AUDIO
//    var soundfx:Bool = false
//    let SND_BUTTON = "button.mp3"
//    var volume:Float = 0.0
//
//
//
//
//    var score:Int = 0
//
//
//
//    enum myScenes:Int {
//        case sceneTitle = 1 ,sceneSelectA, sceneOptions, sceneHelp,sceneControls, sceneGame,sceneSaver
//    }
//
//    enum bonuses:Int {
//        case magnet = 1, invincible, freeze, slowmo ,bigjar, killallreds
//    }
//
//    enum states:Int {
//        case playing = 0, nextlevel,paused,gameover,betweenlevels
//    }
//
//
//    //MARK: -
//    //MARK: physics vars -
//    enum BodyCategory:UInt32 {
//        case nothing = 0
//        case hero = 1
//        case bullet = 2
//        case rock = 4
//        case bonus = 8
//        case anotherBody3 = 16
//        case flame = 32
//    }
//
//
//
//    var currentScene:myScenes?
//    var gameState:states?
//
//    var kNewRockAndOrBonus = 0
//    var kROCK_BIG = 1.0
//    var kROCK_MED = 2.0
//    var kROCK_SML = 3.0
//
//
//
//
//    var currentBonus = 0 //0
//    var isMachineGun = false
//    var isInvincible = false
//
//    var isFrozen = false
//    var isSlow = false
//
//
//
//    var slowDownAmount:CGFloat = 1.0 // 1.0 is normal speed
//    var bigJarBonus = false
//
//    var shipSpeed:CGFloat = 0.0
//    var speedLimit:CGFloat = 0.0
//
//
//    let kBONUS_MAGNET = 1
//    let kBONUS_FREEZE = 2
//
//    var mainGameViewController:GameViewController!
//
//    fileprivate init() {
//
////       volume = SDCloudUserDefaults.float(forKey: "volume")
////       soundfx =  SDCloudUserDefaults.bool(forKey: "soundfx")
////
////        // CHECK FOR NaN!!!!!!!!
////        speedMod =  SDCloudUserDefaults.integer(forKey: "speedMod")
////
////        if (speedMod == 0){
////
////            speedMod = 5; //default
////            SDCloudUserDefaults.setInteger(5, forKey: "speedMod")
////            SDCloudUserDefaults.synchronize();
////
////        }
//
//
//    }
//
//    func startRetroShader(_ theScene:SKScene){
//
//
//
////        let shader_move:SKShader = SKShader(fileNamed: "retro.fsh")
////        let vector = vector2(Float(1920.0), Float(1080))
////
////        shader_move.uniforms = [
////            SKUniform(name: "iChannel0", texture: SKTexture(imageNamed: "dummy2")),
//////            SKUniform(name: "iResolution", float: GLKVector2Make(1920.0, 1080))
////            SKUniform(name: "size", vectorFloat2: vector)
//// ]
////
////        theScene.shader = shader_move
////        theScene.shouldEnableEffects = true
//
//
//        /* // shaderContainer_move.position = CGPointMake(self.frame.size.width/2, self.frame.size.height/2);
//        // shaderContainer_move.size = CGSizeMake(self.frame.size.width, self.frame.size.height);
//        // [self addChild:shaderContainer_move];
//        //Create the shader from a shader-file
//        //SKShader* shader_move = [SKShader shaderWithFileNamed:@"shader_water_movement.fsh"];
//        //Set vairiables that are used in the shader script
//        shader_move.uniforms = @[
//        [SKUniform uniformWithName:@"size" floatVector3:GLKVector3Make(self.frame.size.width*1.5, self.frame.size.height*1.5, 0)],
//        [SKUniform uniformWithName:@"customTexture" texture:[SKTexture textureWithImageNamed:@"sand.png"]],
//        ];
//        //add the shader to the sprite
//        shaderContainer_move.shader = shader_move;
//        */
//
//
//    }
//
//
//    // Etc...
//}
