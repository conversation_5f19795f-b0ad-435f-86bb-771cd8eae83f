//
//  PurchaseManager.swift
//  Template
//
//  Created by Augment Agent on 8/19/25.
//

import Foundation
import StoreKitTheKit

/// Centralized manager for handling all in-app purchase operations
class PurchaseManager: ObservableObject {
    
    static let shared = PurchaseManager()
    
    // MARK: - Constants
    private let removeAdsProductID = "com.markandrade.debrisfield.removeads"
    
    // MARK: - Published Properties
    @Published var isPurchasing = false
    @Published var isRestoring = false
    @Published var storeAvailable = false
    
    // MARK: - Private Properties
    private var removeAdsProduct: Purchasable?
    
    private init() {}
    
    // MARK: - Initialization
    
    /// Initialize StoreKitTheKit with product configuration
    func initializeStore() async {
        // Create purchasable items
        removeAdsProduct = Purchasable(bundleId: removeAdsProductID, type: .nonConsumable)

        guard let removeAdsProduct = removeAdsProduct else {
            print("❌ Failed to create purchasable product")
            return
        }

        // Initialize StoreKitTheKit
        await StoreKitTheKit.shared.start(iapItems: [removeAdsProduct])

        // Sync with store
        await syncWithStore()

        print("✅ StoreKitTheKit initialized successfully")
    }
    
    /// Sync purchases with the App Store
    func syncWithStore() async {
        await StoreKitTheKit.shared.syncWithStore()
        await MainActor.run {
            storeAvailable = StoreKitTheKit.shared.storeState == .available
        }
    }
    
    // MARK: - Purchase Operations
    
    /// Purchase the remove ads product
    func purchaseRemoveAds() async -> PurchaseResult {
        guard let product = removeAdsProduct else {
            return .failure(.productNotFound)
        }

        await MainActor.run {
            isPurchasing = true
        }

        defer {
            Task { @MainActor in
                isPurchasing = false
            }
        }

        // Perform the purchase
        let _ = await StoreKitTheKit.shared.purchaseElement(element: product)

        // Check if the purchase was successful by checking the purchase status
        if StoreKitTheKit.shared.elementWasPurchased(element: product) {
            // Save purchase state
            UserPrefs.shared.saveDatShit(true)
            return .success
        } else {
            // Purchase was not successful
            return .failure(.unknown)
        }
    }
    
    /// Restore previous purchases
    func restorePurchases() async -> RestoreResult {
        await MainActor.run {
            isRestoring = true
        }

        defer {
            Task { @MainActor in
                isRestoring = false
            }
        }

        await StoreKitTheKit.shared.restorePurchases()

        // Check if remove ads was restored
        if let product = removeAdsProduct,
           StoreKitTheKit.shared.elementWasPurchased(element: product) {
            UserPrefs.shared.saveDatShit(true)
            return .success(restoredCount: 1)
        } else {
            return .success(restoredCount: 0)
        }
    }
    
    // MARK: - Purchase Status
    
    /// Check if remove ads has been purchased
    var hasRemoveAdsPurchase: Bool {
        guard let product = removeAdsProduct else { return false }
        return StoreKitTheKit.shared.elementWasPurchased(element: product)
    }
    
    /// Get formatted price for remove ads product
    var removeAdsPrice: String? {
        guard let product = removeAdsProduct else { return nil }
        return StoreKitTheKit.shared.getPriceFormatted(for: product)
    }
}

// MARK: - Result Types

enum PurchaseResult {
    case success
    case failure(PurchaseError)
}

enum RestoreResult {
    case success(restoredCount: Int)
    case failure(PurchaseError)
}

enum PurchaseError {
    case productNotFound
    case userCancelled
    case purchasePending
    case storeUnavailable
    case networkError
    case unknown
    case storeError(Error)
    
    var localizedDescription: String {
        switch self {
        case .productNotFound:
            return "Product not found. Please try again later."
        case .userCancelled:
            return "Purchase was cancelled."
        case .purchasePending:
            return "Purchase is pending approval."
        case .storeUnavailable:
            return "App Store is currently unavailable. Please check your internet connection."
        case .networkError:
            return "Network error. Please check your internet connection and try again."
        case .unknown:
            return "An unknown error occurred. Please try again."
        case .storeError(let error):
            return "Store error: \(error.localizedDescription)"
        }
    }
    
    var isRetryable: Bool {
        switch self {
        case .storeUnavailable, .networkError, .unknown, .storeError:
            return true
        case .productNotFound, .userCancelled, .purchasePending:
            return false
        }
    }
}
