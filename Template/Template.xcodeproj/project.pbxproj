// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		4D01A9D82630FA3C00785EFD /* GameWindow.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D01A9D52630FA3C00785EFD /* GameWindow.swift */; };
		4D01A9DA2631445C00785EFD /* LogoBumperScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D01A9D92631445C00785EFD /* LogoBumperScene.sks */; };
		4D01A9DB2631445C00785EFD /* LogoBumperScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D01A9D92631445C00785EFD /* LogoBumperScene.sks */; };
		4D01A9DC2631445C00785EFD /* LogoBumperScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D01A9D92631445C00785EFD /* LogoBumperScene.sks */; };
		4D029917261D63DF0006EB66 /* MenuButtonNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D467B2D261D5EBD007C8A26 /* MenuButtonNode.swift */; };
		4D029918261D63E00006EB66 /* MenuButtonNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D467B2D261D5EBD007C8A26 /* MenuButtonNode.swift */; };
		4D02991A261F8CFA0006EB66 /* SKAction-extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D029919261F8CFA0006EB66 /* SKAction-extensions.swift */; };
		4D02991B261F8CFA0006EB66 /* SKAction-extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D029919261F8CFA0006EB66 /* SKAction-extensions.swift */; };
		4D02991C261F8CFA0006EB66 /* SKAction-extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D029919261F8CFA0006EB66 /* SKAction-extensions.swift */; };
		4D02991E261F904A0006EB66 /* SettingsScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D02991D261F904A0006EB66 /* SettingsScene.sks */; };
		4D02991F261F904A0006EB66 /* SettingsScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D02991D261F904A0006EB66 /* SettingsScene.sks */; };
		4D029920261F904A0006EB66 /* SettingsScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D02991D261F904A0006EB66 /* SettingsScene.sks */; };
		4D029922261F90F70006EB66 /* SettingsScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D029921261F90F70006EB66 /* SettingsScene.swift */; };
		4D029923261F90F70006EB66 /* SettingsScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D029921261F90F70006EB66 /* SettingsScene.swift */; };
		4D029924261F90F70006EB66 /* SettingsScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D029921261F90F70006EB66 /* SettingsScene.swift */; };
		4D0AB2B9262E83B1008F2B9A /* Button Names.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D0AB2B8262E83B1008F2B9A /* Button Names.txt */; };
		4D0AB2BA262E83B1008F2B9A /* Button Names.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D0AB2B8262E83B1008F2B9A /* Button Names.txt */; };
		4D0AB2BB262E83B1008F2B9A /* Button Names.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D0AB2B8262E83B1008F2B9A /* Button Names.txt */; };
		4D0AB2BD262E862C008F2B9A /* Foundation+KeyboardInput.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0AB2BC262E862C008F2B9A /* Foundation+KeyboardInput.swift */; };
		4D0AB2BE262E862C008F2B9A /* Foundation+KeyboardInput.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0AB2BC262E862C008F2B9A /* Foundation+KeyboardInput.swift */; };
		4D0AB2BF262E862C008F2B9A /* Foundation+KeyboardInput.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0AB2BC262E862C008F2B9A /* Foundation+KeyboardInput.swift */; };
		4D0AB2C1262E8691008F2B9A /* FoundationScene+MouseInput.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0AB2C0262E8691008F2B9A /* FoundationScene+MouseInput.swift */; };
		4D0AB2C2262E8691008F2B9A /* FoundationScene+MouseInput.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0AB2C0262E8691008F2B9A /* FoundationScene+MouseInput.swift */; };
		4D0AB2C3262E8691008F2B9A /* FoundationScene+MouseInput.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0AB2C0262E8691008F2B9A /* FoundationScene+MouseInput.swift */; };
		4D0AB2C9262F51B8008F2B9A /* Notes.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D0AB2C8262F51B8008F2B9A /* Notes.txt */; };
		4D0AB2CA262F51B8008F2B9A /* Notes.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D0AB2C8262F51B8008F2B9A /* Notes.txt */; };
		4D0AB2CB262F51B8008F2B9A /* Notes.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D0AB2C8262F51B8008F2B9A /* Notes.txt */; };
		4D0F93CB2628B6C8002AE26D /* Prefs.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0F93CA2628B6C8002AE26D /* Prefs.swift */; };
		4D0F93CC2628B6C8002AE26D /* Prefs.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0F93CA2628B6C8002AE26D /* Prefs.swift */; };
		4D0F93CD2628B6C8002AE26D /* Prefs.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0F93CA2628B6C8002AE26D /* Prefs.swift */; };
		4D0F93CF2628ED71002AE26D /* UIButton_Music.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0F93CE2628ED71002AE26D /* UIButton_Music.swift */; };
		4D0F93D02628ED71002AE26D /* UIButton_Music.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0F93CE2628ED71002AE26D /* UIButton_Music.swift */; };
		4D0F93D12628ED71002AE26D /* UIButton_Music.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D0F93CE2628ED71002AE26D /* UIButton_Music.swift */; };
		4D12E51426290573006B7624 /* UIButton_Pause.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E51326290573006B7624 /* UIButton_Pause.swift */; };
		4D12E51526290573006B7624 /* UIButton_Pause.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E51326290573006B7624 /* UIButton_Pause.swift */; };
		4D12E51626290573006B7624 /* UIButton_Pause.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E51326290573006B7624 /* UIButton_Pause.swift */; };
		4D12E5182629245A006B7624 /* SceneManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E5172629245A006B7624 /* SceneManager.swift */; };
		4D12E5192629245A006B7624 /* SceneManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E5172629245A006B7624 /* SceneManager.swift */; };
		4D12E51A2629245A006B7624 /* SceneManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E5172629245A006B7624 /* SceneManager.swift */; };
		4D12E51C262933D9006B7624 /* VirtualButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E51B262933D9006B7624 /* VirtualButton.swift */; };
		4D12E51D262933D9006B7624 /* VirtualButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E51B262933D9006B7624 /* VirtualButton.swift */; };
		4D12E51E262933D9006B7624 /* VirtualButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D12E51B262933D9006B7624 /* VirtualButton.swift */; };
		4D14331F262570BA0001472E /* GameKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4D14331E262570BA0001472E /* GameKit.framework */; };
		4D17F2B8262A7FF40073BE22 /* ScoreText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D17F2B7262A7FF40073BE22 /* ScoreText.swift */; };
		4D17F2B9262A7FF40073BE22 /* ScoreText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D17F2B7262A7FF40073BE22 /* ScoreText.swift */; };
		4D17F2BA262A7FF40073BE22 /* ScoreText.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D17F2B7262A7FF40073BE22 /* ScoreText.swift */; };
		4D1BD4EB261FB29A006E71ED /* HelpScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD4EA261FB29A006E71ED /* HelpScene.sks */; };
		4D1BD4EC261FB29A006E71ED /* HelpScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD4EA261FB29A006E71ED /* HelpScene.sks */; };
		4D1BD4ED261FB29A006E71ED /* HelpScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD4EA261FB29A006E71ED /* HelpScene.sks */; };
		4D1BD4EF261FB364006E71ED /* HelpScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD4EE261FB364006E71ED /* HelpScene.swift */; };
		4D1BD4F0261FB364006E71ED /* HelpScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD4EE261FB364006E71ED /* HelpScene.swift */; };
		4D1BD4F1261FB364006E71ED /* HelpScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD4EE261FB364006E71ED /* HelpScene.swift */; };
		4D1BD4F7261FBF69006E71ED /* MoreGamesScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD4F6261FBF69006E71ED /* MoreGamesScene.sks */; };
		4D1BD4F8261FBF69006E71ED /* MoreGamesScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD4F6261FBF69006E71ED /* MoreGamesScene.sks */; };
		4D1BD4F9261FBF69006E71ED /* MoreGamesScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD4F6261FBF69006E71ED /* MoreGamesScene.sks */; };
		4D1BD4FB261FBFCA006E71ED /* MoreGamesScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD4FA261FBFCA006E71ED /* MoreGamesScene.swift */; };
		4D1BD4FC261FBFCA006E71ED /* MoreGamesScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD4FA261FBFCA006E71ED /* MoreGamesScene.swift */; };
		4D1BD4FD261FBFCA006E71ED /* MoreGamesScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD4FA261FBFCA006E71ED /* MoreGamesScene.swift */; };
		4D1BD505261FD983006E71ED /* Protocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD504261FD983006E71ED /* Protocols.swift */; };
		4D1BD506261FD983006E71ED /* Protocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD504261FD983006E71ED /* Protocols.swift */; };
		4D1BD507261FD983006E71ED /* Protocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D1BD504261FD983006E71ED /* Protocols.swift */; };
		4D1BD50D261FE11E006E71ED /* retro.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD50C261FE11E006E71ED /* retro.fsh */; };
		4D1BD50E261FE11E006E71ED /* retro.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD50C261FE11E006E71ED /* retro.fsh */; };
		4D1BD50F261FE11E006E71ED /* retro.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4D1BD50C261FE11E006E71ED /* retro.fsh */; };
		4D25F898262F7ECD00A08FC1 /* UIButton_GameCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D25F897262F7ECD00A08FC1 /* UIButton_GameCenter.swift */; };
		4D25F899262F7ECD00A08FC1 /* UIButton_GameCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D25F897262F7ECD00A08FC1 /* UIButton_GameCenter.swift */; };
		4D25F89A262F7ECD00A08FC1 /* UIButton_GameCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D25F897262F7ECD00A08FC1 /* UIButton_GameCenter.swift */; };
		4D3BB10D261D4F2400E9CCAC /* ui.atlas in Resources */ = {isa = PBXBuildFile; fileRef = 4D3BB10C261D4F2400E9CCAC /* ui.atlas */; };
		4D3BB10E261D4F2400E9CCAC /* ui.atlas in Resources */ = {isa = PBXBuildFile; fileRef = 4D3BB10C261D4F2400E9CCAC /* ui.atlas */; };
		4D3BB10F261D4F2400E9CCAC /* ui.atlas in Resources */ = {isa = PBXBuildFile; fileRef = 4D3BB10C261D4F2400E9CCAC /* ui.atlas */; };
		4D3BB112261D524600E9CCAC /* MenuScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D3BB111261D524600E9CCAC /* MenuScene.sks */; };
		4D3BB113261D524600E9CCAC /* MenuScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D3BB111261D524600E9CCAC /* MenuScene.sks */; };
		4D3BB114261D524600E9CCAC /* MenuScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D3BB111261D524600E9CCAC /* MenuScene.sks */; };
		4D42B135262025D200F464D7 /* SHKPassthrough.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4D42B134262025D200F464D7 /* SHKPassthrough.fsh */; };
		4D42B136262025D200F464D7 /* SHKPassthrough.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4D42B134262025D200F464D7 /* SHKPassthrough.fsh */; };
		4D42B137262025D200F464D7 /* SHKPassthrough.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4D42B134262025D200F464D7 /* SHKPassthrough.fsh */; };
		4D467B2E261D5EBD007C8A26 /* MenuButtonNode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D467B2D261D5EBD007C8A26 /* MenuButtonNode.swift */; };
		4D48AC302E4F93DF0065DFB5 /* ToDo.md in Resources */ = {isa = PBXBuildFile; fileRef = 4D48AC2F2E4F93DF0065DFB5 /* ToDo.md */; };
		4D48AC312E4F93DF0065DFB5 /* ToDo.md in Resources */ = {isa = PBXBuildFile; fileRef = 4D48AC2F2E4F93DF0065DFB5 /* ToDo.md */; };
		4D48AC322E4F93DF0065DFB5 /* ToDo.md in Resources */ = {isa = PBXBuildFile; fileRef = 4D48AC2F2E4F93DF0065DFB5 /* ToDo.md */; };
		4D5A1FD62613D13B006F96FA /* Bullet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FD52613D13B006F96FA /* Bullet.swift */; };
		4D5A1FD72613D13B006F96FA /* Bullet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FD52613D13B006F96FA /* Bullet.swift */; };
		4D5A1FD82613D13B006F96FA /* Bullet.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FD52613D13B006F96FA /* Bullet.swift */; };
		4D5A1FDA2613D4F9006F96FA /* Guns.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FD92613D4F9006F96FA /* Guns.swift */; };
		4D5A1FDB2613D4F9006F96FA /* Guns.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FD92613D4F9006F96FA /* Guns.swift */; };
		4D5A1FDC2613D4F9006F96FA /* Guns.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FD92613D4F9006F96FA /* Guns.swift */; };
		4D5A1FE326152A55006F96FA /* Rocks.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FE126152A55006F96FA /* Rocks.swift */; };
		4D5A1FE426152A55006F96FA /* Rocks.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FE126152A55006F96FA /* Rocks.swift */; };
		4D5A1FE526152A55006F96FA /* Rocks.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FE126152A55006F96FA /* Rocks.swift */; };
		4D5A1FE626152A55006F96FA /* Rock.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FE226152A55006F96FA /* Rock.swift */; };
		4D5A1FE726152A55006F96FA /* Rock.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FE226152A55006F96FA /* Rock.swift */; };
		4D5A1FE826152A55006F96FA /* Rock.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D5A1FE226152A55006F96FA /* Rock.swift */; };
		4D6C88D62625169100E03680 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4D6C88D52625169100E03680 /* GameController.framework */; };
		4D6C88D82625169100E03680 /* GameKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4D6C88D72625169100E03680 /* GameKit.framework */; };
		4D6C88DA262518CF00E03680 /* GameCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D6C88D9262518CF00E03680 /* GameCenter.swift */; };
		4D6C88DB262518CF00E03680 /* GameCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D6C88D9262518CF00E03680 /* GameCenter.swift */; };
		4D6C88DC262518CF00E03680 /* GameCenter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D6C88D9262518CF00E03680 /* GameCenter.swift */; };
		4D84DD35262F9CC800661737 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4D84DD34262F9CC800661737 /* GameController.framework */; };
		4D9430102626B879001AB0B7 /* OldCode.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D94300F2626B879001AB0B7 /* OldCode.txt */; };
		4D9430112626B879001AB0B7 /* OldCode.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D94300F2626B879001AB0B7 /* OldCode.txt */; };
		4D9430122626B879001AB0B7 /* OldCode.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4D94300F2626B879001AB0B7 /* OldCode.txt */; };
		4D95FA302E4EA144007116F9 /* PurchaseWaitScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D95FA2F2E4EA144007116F9 /* PurchaseWaitScene.sks */; };
		4D95FA312E4EA144007116F9 /* PurchaseWaitScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D95FA2F2E4EA144007116F9 /* PurchaseWaitScene.sks */; };
		4D95FA322E4EA144007116F9 /* PurchaseWaitScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4D95FA2F2E4EA144007116F9 /* PurchaseWaitScene.sks */; };
		4D95FA342E4EC75C007116F9 /* mark-logo.png in Resources */ = {isa = PBXBuildFile; fileRef = 4D95FA332E4EC75C007116F9 /* mark-logo.png */; };
		4D95FA352E4EC75C007116F9 /* mark-logo.png in Resources */ = {isa = PBXBuildFile; fileRef = 4D95FA332E4EC75C007116F9 /* mark-logo.png */; };
		4D95FA362E4EC75C007116F9 /* mark-logo.png in Resources */ = {isa = PBXBuildFile; fileRef = 4D95FA332E4EC75C007116F9 /* mark-logo.png */; };
		4D95FA392E4ED773007116F9 /* StoreKitTheKit in Frameworks */ = {isa = PBXBuildFile; productRef = 4D95FA382E4ED773007116F9 /* StoreKitTheKit */; };
		4DA4A5182E34085D00178304 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A5172E34085700178304 /* Constants.swift */; };
		4DA4A5192E34085D00178304 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A5172E34085700178304 /* Constants.swift */; };
		4DA4A51A2E34085D00178304 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A5172E34085700178304 /* Constants.swift */; };
		4DA4A51B2E34085D00178304 /* PurchaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A51C2E34085700178304 /* PurchaseManager.swift */; };
		4DA4A51D2E34085D00178304 /* PurchaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A51C2E34085700178304 /* PurchaseManager.swift */; };
		4DA4A51E2E34085D00178304 /* PurchaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A51C2E34085700178304 /* PurchaseManager.swift */; };
		4DA4A51F2E34085D00178304 /* PurchaseManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A5202E34085700178304 /* PurchaseManagerTests.swift */; };
		4DA4A5212E34085D00178304 /* PurchaseManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A5202E34085700178304 /* PurchaseManagerTests.swift */; };
		4DA4A5222E34085D00178304 /* PurchaseManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DA4A5202E34085700178304 /* PurchaseManagerTests.swift */; };
		4DAF33962628F65100CBEF83 /* UIButton_Sound.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF33952628F65100CBEF83 /* UIButton_Sound.swift */; };
		4DAF33972628F65100CBEF83 /* UIButton_Sound.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF33952628F65100CBEF83 /* UIButton_Sound.swift */; };
		4DAF33982628F65100CBEF83 /* UIButton_Sound.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF33952628F65100CBEF83 /* UIButton_Sound.swift */; };
		4DAF339A2628F6D900CBEF83 /* UIButton_NavigateToScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF33992628F6D900CBEF83 /* UIButton_NavigateToScene.swift */; };
		4DAF339B2628F6D900CBEF83 /* UIButton_NavigateToScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF33992628F6D900CBEF83 /* UIButton_NavigateToScene.swift */; };
		4DAF339C2628F6D900CBEF83 /* UIButton_NavigateToScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DAF33992628F6D900CBEF83 /* UIButton_NavigateToScene.swift */; };
		4DBD99D22631EA0A008B9445 /* LogoBumperScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DBD99D12631EA0A008B9445 /* LogoBumperScene.swift */; };
		4DBD99D32631EA0A008B9445 /* LogoBumperScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DBD99D12631EA0A008B9445 /* LogoBumperScene.swift */; };
		4DBD99D42631EA0A008B9445 /* LogoBumperScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DBD99D12631EA0A008B9445 /* LogoBumperScene.swift */; };
		4DC562E929DE31BF00D0E820 /* UIButton_Back.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562E829DE31BF00D0E820 /* UIButton_Back.swift */; };
		4DC562EA29DE31BF00D0E820 /* UIButton_Back.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562E829DE31BF00D0E820 /* UIButton_Back.swift */; };
		4DC562EB29DE31BF00D0E820 /* UIButton_Back.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562E829DE31BF00D0E820 /* UIButton_Back.swift */; };
		4DC562ED29DE337500D0E820 /* UIButton_RepositionButtons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562EC29DE337500D0E820 /* UIButton_RepositionButtons.swift */; };
		4DC562EE29DE337500D0E820 /* UIButton_RepositionButtons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562EC29DE337500D0E820 /* UIButton_RepositionButtons.swift */; };
		4DC562EF29DE337500D0E820 /* UIButton_RepositionButtons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562EC29DE337500D0E820 /* UIButton_RepositionButtons.swift */; };
		4DC562F129DE337E00D0E820 /* UIButton_ToggleControls.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F029DE337E00D0E820 /* UIButton_ToggleControls.swift */; };
		4DC562F229DE337E00D0E820 /* UIButton_ToggleControls.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F029DE337E00D0E820 /* UIButton_ToggleControls.swift */; };
		4DC562F329DE337E00D0E820 /* UIButton_ToggleControls.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F029DE337E00D0E820 /* UIButton_ToggleControls.swift */; };
		4DC562F729DE338700D0E820 /* UIButton_RestorePurchase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F429DE338700D0E820 /* UIButton_RestorePurchase.swift */; };
		4DC562F829DE338700D0E820 /* UIButton_RestorePurchase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F429DE338700D0E820 /* UIButton_RestorePurchase.swift */; };
		4DC562F929DE338700D0E820 /* UIButton_RestorePurchase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F429DE338700D0E820 /* UIButton_RestorePurchase.swift */; };
		4DC562FA29DE338700D0E820 /* UIButton_Continue.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F529DE338700D0E820 /* UIButton_Continue.swift */; };
		4DC562FB29DE338700D0E820 /* UIButton_Continue.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F529DE338700D0E820 /* UIButton_Continue.swift */; };
		4DC562FC29DE338700D0E820 /* UIButton_Continue.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F529DE338700D0E820 /* UIButton_Continue.swift */; };
		4DC562FD29DE338700D0E820 /* UIButton_Purchase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F629DE338700D0E820 /* UIButton_Purchase.swift */; };
		4DC562FE29DE338700D0E820 /* UIButton_Purchase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F629DE338700D0E820 /* UIButton_Purchase.swift */; };
		4DC562FF29DE338700D0E820 /* UIButton_Purchase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC562F629DE338700D0E820 /* UIButton_Purchase.swift */; };
		4DC5630329DE341300D0E820 /* sfxButton_play.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630029DE341300D0E820 /* sfxButton_play.mp3 */; };
		4DC5630429DE341300D0E820 /* sfxButton_play.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630029DE341300D0E820 /* sfxButton_play.mp3 */; };
		4DC5630529DE341300D0E820 /* sfxButton_play.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630029DE341300D0E820 /* sfxButton_play.mp3 */; };
		4DC5630629DE341300D0E820 /* sfxButton_back.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630129DE341300D0E820 /* sfxButton_back.mp3 */; };
		4DC5630729DE341300D0E820 /* sfxButton_back.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630129DE341300D0E820 /* sfxButton_back.mp3 */; };
		4DC5630829DE341300D0E820 /* sfxButton_back.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630129DE341300D0E820 /* sfxButton_back.mp3 */; };
		4DC5630929DE341300D0E820 /* sfxButton_select.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630229DE341300D0E820 /* sfxButton_select.mp3 */; };
		4DC5630A29DE341300D0E820 /* sfxButton_select.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630229DE341300D0E820 /* sfxButton_select.mp3 */; };
		4DC5630B29DE341300D0E820 /* sfxButton_select.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5630229DE341300D0E820 /* sfxButton_select.mp3 */; };
		4DC5630D29DE34A500D0E820 /* PurchaseWaitScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5630C29DE34A500D0E820 /* PurchaseWaitScene.swift */; };
		4DC5630E29DE34A500D0E820 /* PurchaseWaitScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5630C29DE34A500D0E820 /* PurchaseWaitScene.swift */; };
		4DC5630F29DE34A500D0E820 /* PurchaseWaitScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5630C29DE34A500D0E820 /* PurchaseWaitScene.swift */; };
		4DC5659325E99A070093BF67 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5659225E99A070093BF67 /* AppDelegate.swift */; };
		4DC5659525E99A070093BF67 /* GameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5659425E99A070093BF67 /* GameViewController.swift */; };
		4DC5659825E99A070093BF67 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5659625E99A070093BF67 /* Main.storyboard */; };
		4DC5659B25E99A070093BF67 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5659925E99A070093BF67 /* LaunchScreen.storyboard */; };
		4DC565A425E99A070093BF67 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565A325E99A070093BF67 /* AppDelegate.swift */; };
		4DC565A625E99A070093BF67 /* GameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565A525E99A070093BF67 /* GameViewController.swift */; };
		4DC565A925E99A070093BF67 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4DC565A725E99A070093BF67 /* Main.storyboard */; };
		4DC565AC25E99A070093BF67 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4DC565AA25E99A070093BF67 /* LaunchScreen.storyboard */; };
		4DC565B525E99A070093BF67 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565B425E99A070093BF67 /* AppDelegate.swift */; };
		4DC565B725E99A070093BF67 /* GameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565B625E99A070093BF67 /* GameViewController.swift */; };
		4DC565BA25E99A070093BF67 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4DC565B825E99A070093BF67 /* Main.storyboard */; };
		4DC565C025E99A070093BF67 /* GameScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5658825E99A060093BF67 /* GameScene.sks */; };
		4DC565C125E99A070093BF67 /* GameScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5658825E99A060093BF67 /* GameScene.sks */; };
		4DC565C225E99A070093BF67 /* GameScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5658825E99A060093BF67 /* GameScene.sks */; };
		4DC565C325E99A070093BF67 /* GameScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5658925E99A060093BF67 /* GameScene.swift */; };
		4DC565C425E99A070093BF67 /* GameScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5658925E99A060093BF67 /* GameScene.swift */; };
		4DC565C525E99A070093BF67 /* GameScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5658925E99A060093BF67 /* GameScene.swift */; };
		4DC565C625E99A070093BF67 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5658A25E99A070093BF67 /* Assets.xcassets */; };
		4DC565C725E99A070093BF67 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5658A25E99A070093BF67 /* Assets.xcassets */; };
		4DC565C825E99A070093BF67 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4DC5658A25E99A070093BF67 /* Assets.xcassets */; };
		4DC565DB25E9B7270093BF67 /* Ship.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565DA25E9B7270093BF67 /* Ship.swift */; };
		4DC565DC25E9B7270093BF67 /* Ship.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565DA25E9B7270093BF67 /* Ship.swift */; };
		4DC565DD25E9B7270093BF67 /* Ship.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565DA25E9B7270093BF67 /* Ship.swift */; };
		4DC565E225E9B7840093BF67 /* GameData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565E125E9B7840093BF67 /* GameData.swift */; };
		4DC565E325E9B7840093BF67 /* GameData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565E125E9B7840093BF67 /* GameData.swift */; };
		4DC565E425E9B7840093BF67 /* GameData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC565E125E9B7840093BF67 /* GameData.swift */; };
		4DC565E925E9B9810093BF67 /* sprite.atlas in Resources */ = {isa = PBXBuildFile; fileRef = 4DC565E825E9B9810093BF67 /* sprite.atlas */; };
		4DC565EA25E9B9810093BF67 /* sprite.atlas in Resources */ = {isa = PBXBuildFile; fileRef = 4DC565E825E9B9810093BF67 /* sprite.atlas */; };
		4DC565EB25E9B9810093BF67 /* sprite.atlas in Resources */ = {isa = PBXBuildFile; fileRef = 4DC565E825E9B9810093BF67 /* sprite.atlas */; };
		4DC5660E25E9BD130093BF67 /* AATools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5660D25E9BD130093BF67 /* AATools.swift */; };
		4DC5660F25E9BD130093BF67 /* AATools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5660D25E9BD130093BF67 /* AATools.swift */; };
		4DC5661025E9BD130093BF67 /* AATools.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DC5660D25E9BD130093BF67 /* AATools.swift */; };
		4DD41C34261CDCE800F31FE5 /* GameKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4DD41C33261CDCE800F31FE5 /* GameKit.framework */; };
		4DD41C36261CDCEF00F31FE5 /* GameController.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4DD41C35261CDCEF00F31FE5 /* GameController.framework */; };
		4DD8C1582E356F02007F4AEF /* UIButton_ResetButtons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C1572E356F02007F4AEF /* UIButton_ResetButtons.swift */; };
		4DD8C1592E356F02007F4AEF /* UIButton_ResetButtons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C1572E356F02007F4AEF /* UIButton_ResetButtons.swift */; };
		4DD8C15A2E356F02007F4AEF /* UIButton_ResetButtons.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C1572E356F02007F4AEF /* UIButton_ResetButtons.swift */; };
		4DD8C1602E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C15F2E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift */; };
		4DD8C1612E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C15F2E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift */; };
		4DD8C1622E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C15F2E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift */; };
		4DD8C1642E35B2ED007F4AEF /* BounceAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C1632E35B2E2007F4AEF /* BounceAnimation.swift */; };
		4DD8C1652E35B2ED007F4AEF /* BounceAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C1632E35B2E2007F4AEF /* BounceAnimation.swift */; };
		4DD8C1662E35B2ED007F4AEF /* BounceAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD8C1632E35B2E2007F4AEF /* BounceAnimation.swift */; };
		4DD95DD4262018D600477598 /* SHKPixelate.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4DD95DD3262018D600477598 /* SHKPixelate.fsh */; };
		4DD95DD5262018D600477598 /* SHKPixelate.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4DD95DD3262018D600477598 /* SHKPixelate.fsh */; };
		4DD95DD6262018D600477598 /* SHKPixelate.fsh in Resources */ = {isa = PBXBuildFile; fileRef = 4DD95DD3262018D600477598 /* SHKPixelate.fsh */; };
		4DD95DD826201AB100477598 /* ShaderKitExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD95DD726201AB100477598 /* ShaderKitExtensions.swift */; };
		4DD95DD926201AB100477598 /* ShaderKitExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD95DD726201AB100477598 /* ShaderKitExtensions.swift */; };
		4DD95DDA26201AB100477598 /* ShaderKitExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DD95DD726201AB100477598 /* ShaderKitExtensions.swift */; };
		4DDF011A2616ABBB001D8410 /* FoundationScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DDF01192616ABBB001D8410 /* FoundationScene.swift */; };
		4DDF011B2616ABBB001D8410 /* FoundationScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DDF01192616ABBB001D8410 /* FoundationScene.swift */; };
		4DDF011C2616ABBB001D8410 /* FoundationScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DDF01192616ABBB001D8410 /* FoundationScene.swift */; };
		4DE4616026279D500075C7C8 /* Analytics.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DE4615F26279D500075C7C8 /* Analytics.swift */; };
		4DE4616126279D500075C7C8 /* Analytics.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DE4615F26279D500075C7C8 /* Analytics.swift */; };
		4DE4616226279D500075C7C8 /* Analytics.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DE4615F26279D500075C7C8 /* Analytics.swift */; };
		4DEEFAD425FDE7CC00264B7C /* MenuScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DEEFAD325FDE7CC00264B7C /* MenuScene.swift */; };
		4DEEFAD525FDE7CC00264B7C /* MenuScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DEEFAD325FDE7CC00264B7C /* MenuScene.swift */; };
		4DEEFAD625FDE7CC00264B7C /* MenuScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DEEFAD325FDE7CC00264B7C /* MenuScene.swift */; };
		4DF3CE43262A392C00BA778F /* PositionButtonsScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4DF3CE42262A392C00BA778F /* PositionButtonsScene.sks */; };
		4DF3CE4F262A3B6000BA778F /* PositionButtonsScene.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4DF3CE4E262A3B6000BA778F /* PositionButtonsScene.swift */; };
		4DF3CE52262A3D7600BA778F /* PositionButtonsScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4DF3CE42262A392C00BA778F /* PositionButtonsScene.sks */; };
		4DF3CE53262A3D7600BA778F /* PositionButtonsScene.sks in Resources */ = {isa = PBXBuildFile; fileRef = 4DF3CE42262A392C00BA778F /* PositionButtonsScene.sks */; };
		4DF5A87B26202BAE00F977A9 /* ship3.png in Resources */ = {isa = PBXBuildFile; fileRef = 4DF5A87A26202BAE00F977A9 /* ship3.png */; };
		4DF5A87C26202BAE00F977A9 /* ship3.png in Resources */ = {isa = PBXBuildFile; fileRef = 4DF5A87A26202BAE00F977A9 /* ship3.png */; };
		4DF5A87D26202BAE00F977A9 /* ship3.png in Resources */ = {isa = PBXBuildFile; fileRef = 4DF5A87A26202BAE00F977A9 /* ship3.png */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		4DC565FB25E9BB4C0093BF67 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC5660025E9BB5F0093BF67 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC5660825E9BC310093BF67 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		4D01A9D52630FA3C00785EFD /* GameWindow.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameWindow.swift; sourceTree = "<group>"; };
		4D01A9D92631445C00785EFD /* LogoBumperScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = LogoBumperScene.sks; sourceTree = "<group>"; };
		4D029919261F8CFA0006EB66 /* SKAction-extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "SKAction-extensions.swift"; sourceTree = "<group>"; };
		4D02991D261F904A0006EB66 /* SettingsScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = SettingsScene.sks; sourceTree = "<group>"; };
		4D029921261F90F70006EB66 /* SettingsScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsScene.swift; sourceTree = "<group>"; };
		4D0AB2B8262E83B1008F2B9A /* Button Names.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = "Button Names.txt"; sourceTree = "<group>"; };
		4D0AB2BC262E862C008F2B9A /* Foundation+KeyboardInput.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Foundation+KeyboardInput.swift"; sourceTree = "<group>"; };
		4D0AB2C0262E8691008F2B9A /* FoundationScene+MouseInput.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "FoundationScene+MouseInput.swift"; sourceTree = "<group>"; };
		4D0AB2C8262F51B8008F2B9A /* Notes.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = Notes.txt; sourceTree = "<group>"; };
		4D0F93CA2628B6C8002AE26D /* Prefs.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Prefs.swift; sourceTree = "<group>"; };
		4D0F93CE2628ED71002AE26D /* UIButton_Music.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIButton_Music.swift; sourceTree = "<group>"; };
		4D12E51326290573006B7624 /* UIButton_Pause.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIButton_Pause.swift; sourceTree = "<group>"; };
		4D12E5172629245A006B7624 /* SceneManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneManager.swift; sourceTree = "<group>"; };
		4D12E51B262933D9006B7624 /* VirtualButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VirtualButton.swift; sourceTree = "<group>"; };
		4D14331E262570BA0001472E /* GameKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameKit.framework; path = System/Library/Frameworks/GameKit.framework; sourceTree = SDKROOT; };
		4D17F2B7262A7FF40073BE22 /* ScoreText.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScoreText.swift; sourceTree = "<group>"; };
		4D1BD4EA261FB29A006E71ED /* HelpScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = HelpScene.sks; sourceTree = "<group>"; };
		4D1BD4EE261FB364006E71ED /* HelpScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HelpScene.swift; sourceTree = "<group>"; };
		4D1BD4F6261FBF69006E71ED /* MoreGamesScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = MoreGamesScene.sks; sourceTree = "<group>"; };
		4D1BD4FA261FBFCA006E71ED /* MoreGamesScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MoreGamesScene.swift; sourceTree = "<group>"; };
		4D1BD504261FD983006E71ED /* Protocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Protocols.swift; sourceTree = "<group>"; };
		4D1BD50C261FE11E006E71ED /* retro.fsh */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.glsl; path = retro.fsh; sourceTree = "<group>"; };
		4D25F897262F7ECD00A08FC1 /* UIButton_GameCenter.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_GameCenter.swift; sourceTree = "<group>"; };
		4D3BB10C261D4F2400E9CCAC /* ui.atlas */ = {isa = PBXFileReference; lastKnownFileType = folder.skatlas; name = ui.atlas; path = ../../art/ui.atlas; sourceTree = "<group>"; };
		4D3BB111261D524600E9CCAC /* MenuScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = MenuScene.sks; sourceTree = "<group>"; };
		4D42B134262025D200F464D7 /* SHKPassthrough.fsh */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.glsl; path = SHKPassthrough.fsh; sourceTree = "<group>"; };
		4D467B2D261D5EBD007C8A26 /* MenuButtonNode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuButtonNode.swift; sourceTree = "<group>"; };
		4D48AC2F2E4F93DF0065DFB5 /* ToDo.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = ToDo.md; sourceTree = "<group>"; };
		4D5A1FD52613D13B006F96FA /* Bullet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Bullet.swift; sourceTree = "<group>"; };
		4D5A1FD92613D4F9006F96FA /* Guns.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Guns.swift; sourceTree = "<group>"; };
		4D5A1FE126152A55006F96FA /* Rocks.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Rocks.swift; sourceTree = "<group>"; };
		4D5A1FE226152A55006F96FA /* Rock.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Rock.swift; sourceTree = "<group>"; };
		4D6C88D52625169100E03680 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS14.3.sdk/System/Library/Frameworks/GameController.framework; sourceTree = DEVELOPER_DIR; };
		4D6C88D72625169100E03680 /* GameKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameKit.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS14.3.sdk/System/Library/Frameworks/GameKit.framework; sourceTree = DEVELOPER_DIR; };
		4D6C88D9262518CF00E03680 /* GameCenter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameCenter.swift; sourceTree = "<group>"; };
		4D84DD34262F9CC800661737 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = System/Library/Frameworks/GameController.framework; sourceTree = SDKROOT; };
		4D94300F2626B879001AB0B7 /* OldCode.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = OldCode.txt; sourceTree = "<group>"; };
		4D95FA2F2E4EA144007116F9 /* PurchaseWaitScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = PurchaseWaitScene.sks; sourceTree = "<group>"; };
		4D95FA332E4EC75C007116F9 /* mark-logo.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "mark-logo.png"; sourceTree = "<group>"; };
		4DA4A5172E34085700178304 /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		4DA4A51C2E34085700178304 /* PurchaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseManager.swift; sourceTree = "<group>"; };
		4DA4A5202E34085700178304 /* PurchaseManagerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PurchaseManagerTests.swift; sourceTree = "<group>"; };
		4DAF33952628F65100CBEF83 /* UIButton_Sound.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_Sound.swift; sourceTree = "<group>"; };
		4DAF33992628F6D900CBEF83 /* UIButton_NavigateToScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_NavigateToScene.swift; sourceTree = "<group>"; };
		4DBD99D12631EA0A008B9445 /* LogoBumperScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogoBumperScene.swift; sourceTree = "<group>"; };
		4DC562E829DE31BF00D0E820 /* UIButton_Back.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_Back.swift; sourceTree = "<group>"; };
		4DC562EC29DE337500D0E820 /* UIButton_RepositionButtons.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_RepositionButtons.swift; sourceTree = "<group>"; };
		4DC562F029DE337E00D0E820 /* UIButton_ToggleControls.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_ToggleControls.swift; sourceTree = "<group>"; };
		4DC562F429DE338700D0E820 /* UIButton_RestorePurchase.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_RestorePurchase.swift; sourceTree = "<group>"; };
		4DC562F529DE338700D0E820 /* UIButton_Continue.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_Continue.swift; sourceTree = "<group>"; };
		4DC562F629DE338700D0E820 /* UIButton_Purchase.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UIButton_Purchase.swift; sourceTree = "<group>"; };
		4DC5630029DE341300D0E820 /* sfxButton_play.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = sfxButton_play.mp3; sourceTree = "<group>"; };
		4DC5630129DE341300D0E820 /* sfxButton_back.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = sfxButton_back.mp3; sourceTree = "<group>"; };
		4DC5630229DE341300D0E820 /* sfxButton_select.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = sfxButton_select.mp3; sourceTree = "<group>"; };
		4DC5630C29DE34A500D0E820 /* PurchaseWaitScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PurchaseWaitScene.swift; sourceTree = "<group>"; };
		4DC5658825E99A060093BF67 /* GameScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = GameScene.sks; sourceTree = "<group>"; };
		4DC5658925E99A060093BF67 /* GameScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameScene.swift; sourceTree = "<group>"; };
		4DC5658A25E99A070093BF67 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		4DC5658F25E99A070093BF67 /* Template.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Template.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4DC5659225E99A070093BF67 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		4DC5659425E99A070093BF67 /* GameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameViewController.swift; sourceTree = "<group>"; };
		4DC5659725E99A070093BF67 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		4DC5659A25E99A070093BF67 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		4DC5659C25E99A070093BF67 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4DC565A125E99A070093BF67 /* Template.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Template.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4DC565A325E99A070093BF67 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		4DC565A525E99A070093BF67 /* GameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameViewController.swift; sourceTree = "<group>"; };
		4DC565A825E99A070093BF67 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		4DC565AB25E99A070093BF67 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		4DC565AD25E99A070093BF67 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4DC565B225E99A070093BF67 /* Template.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Template.app; sourceTree = BUILT_PRODUCTS_DIR; };
		4DC565B425E99A070093BF67 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		4DC565B625E99A070093BF67 /* GameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameViewController.swift; sourceTree = "<group>"; };
		4DC565B925E99A070093BF67 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		4DC565BB25E99A070093BF67 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		4DC565BC25E99A070093BF67 /* Template_macOS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Template_macOS.entitlements; sourceTree = "<group>"; };
		4DC565DA25E9B7270093BF67 /* Ship.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Ship.swift; sourceTree = "<group>"; };
		4DC565E125E9B7840093BF67 /* GameData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameData.swift; sourceTree = "<group>"; };
		4DC565E825E9B9810093BF67 /* sprite.atlas */ = {isa = PBXFileReference; lastKnownFileType = folder.skatlas; name = sprite.atlas; path = ../../art/sprite.atlas; sourceTree = "<group>"; };
		4DC5660D25E9BD130093BF67 /* AATools.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AATools.swift; sourceTree = "<group>"; };
		4DD41C33261CDCE800F31FE5 /* GameKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.4.sdk/System/Library/Frameworks/GameKit.framework; sourceTree = DEVELOPER_DIR; };
		4DD41C35261CDCEF00F31FE5 /* GameController.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GameController.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.4.sdk/System/Library/Frameworks/GameController.framework; sourceTree = DEVELOPER_DIR; };
		4DD8C1572E356F02007F4AEF /* UIButton_ResetButtons.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIButton_ResetButtons.swift; sourceTree = "<group>"; };
		4DD8C15F2E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIButton_ResetVirtualBtnPositions.swift; sourceTree = "<group>"; };
		4DD8C1632E35B2E2007F4AEF /* BounceAnimation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BounceAnimation.swift; sourceTree = "<group>"; };
		4DD95DD3262018D600477598 /* SHKPixelate.fsh */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.glsl; path = SHKPixelate.fsh; sourceTree = "<group>"; };
		4DD95DD726201AB100477598 /* ShaderKitExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShaderKitExtensions.swift; sourceTree = "<group>"; };
		4DDF01192616ABBB001D8410 /* FoundationScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoundationScene.swift; sourceTree = "<group>"; };
		4DE4615F26279D500075C7C8 /* Analytics.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Analytics.swift; sourceTree = "<group>"; };
		4DEEFAD325FDE7CC00264B7C /* MenuScene.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuScene.swift; sourceTree = "<group>"; };
		4DF3CE42262A392C00BA778F /* PositionButtonsScene.sks */ = {isa = PBXFileReference; lastKnownFileType = file.sks; path = PositionButtonsScene.sks; sourceTree = "<group>"; };
		4DF3CE4E262A3B6000BA778F /* PositionButtonsScene.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PositionButtonsScene.swift; sourceTree = "<group>"; };
		4DF5A87A26202BAE00F977A9 /* ship3.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = ship3.png; path = ../../art/ship3.png; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4DC5658C25E99A070093BF67 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4DD41C36261CDCEF00F31FE5 /* GameController.framework in Frameworks */,
				4D95FA392E4ED773007116F9 /* StoreKitTheKit in Frameworks */,
				4DD41C34261CDCE800F31FE5 /* GameKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC5659E25E99A070093BF67 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D6C88D62625169100E03680 /* GameController.framework in Frameworks */,
				4D6C88D82625169100E03680 /* GameKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC565AF25E99A070093BF67 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D84DD35262F9CC800661737 /* GameController.framework in Frameworks */,
				4D14331F262570BA0001472E /* GameKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4D61C869261D063E009EBA4D /* Sprites */ = {
			isa = PBXGroup;
			children = (
				4DC565DA25E9B7270093BF67 /* Ship.swift */,
				4D5A1FD52613D13B006F96FA /* Bullet.swift */,
				4D5A1FE226152A55006F96FA /* Rock.swift */,
			);
			path = Sprites;
			sourceTree = "<group>";
		};
		4D61C86A261D0656009EBA4D /* Scenes */ = {
			isa = PBXGroup;
			children = (
				4D6C88D12624F99A00E03680 /* SKS Files */,
				4D467B2D261D5EBD007C8A26 /* MenuButtonNode.swift */,
				4DEEFAD325FDE7CC00264B7C /* MenuScene.swift */,
				4DC5658925E99A060093BF67 /* GameScene.swift */,
				4D1BD4EE261FB364006E71ED /* HelpScene.swift */,
				4DBD99D12631EA0A008B9445 /* LogoBumperScene.swift */,
				4DC5630C29DE34A500D0E820 /* PurchaseWaitScene.swift */,
				4DF3CE4E262A3B6000BA778F /* PositionButtonsScene.swift */,
				4D029921261F90F70006EB66 /* SettingsScene.swift */,
				4D1BD4FA261FBFCA006E71ED /* MoreGamesScene.swift */,
				4D0AB2BC262E862C008F2B9A /* Foundation+KeyboardInput.swift */,
				4D0AB2C0262E8691008F2B9A /* FoundationScene+MouseInput.swift */,
				4DDF01192616ABBB001D8410 /* FoundationScene.swift */,
			);
			path = Scenes;
			sourceTree = "<group>";
		};
		4D61C86B261D0682009EBA4D /* Sprite Managers */ = {
			isa = PBXGroup;
			children = (
				4D5A1FE126152A55006F96FA /* Rocks.swift */,
				4D5A1FD92613D4F9006F96FA /* Guns.swift */,
			);
			path = "Sprite Managers";
			sourceTree = "<group>";
		};
		4D6C88D12624F99A00E03680 /* SKS Files */ = {
			isa = PBXGroup;
			children = (
				4D95FA2F2E4EA144007116F9 /* PurchaseWaitScene.sks */,
				4D3BB111261D524600E9CCAC /* MenuScene.sks */,
				4D1BD4EA261FB29A006E71ED /* HelpScene.sks */,
				4D1BD4F6261FBF69006E71ED /* MoreGamesScene.sks */,
				4D02991D261F904A0006EB66 /* SettingsScene.sks */,
				4DF3CE42262A392C00BA778F /* PositionButtonsScene.sks */,
				4DC5658825E99A060093BF67 /* GameScene.sks */,
				4D01A9D92631445C00785EFD /* LogoBumperScene.sks */,
			);
			path = "SKS Files";
			sourceTree = "<group>";
		};
		4D6C88D22624F9BD00E03680 /* Shaders */ = {
			isa = PBXGroup;
			children = (
				4DD95DD726201AB100477598 /* ShaderKitExtensions.swift */,
				4DD95DD3262018D600477598 /* SHKPixelate.fsh */,
				4D1BD50C261FE11E006E71ED /* retro.fsh */,
				4D42B134262025D200F464D7 /* SHKPassthrough.fsh */,
			);
			path = Shaders;
			sourceTree = "<group>";
		};
		4DAF339D2628F77700CBEF83 /* UIButtons */ = {
			isa = PBXGroup;
			children = (
				4D12E51B262933D9006B7624 /* VirtualButton.swift */,
				4D0F93CE2628ED71002AE26D /* UIButton_Music.swift */,
				4DD8C1572E356F02007F4AEF /* UIButton_ResetButtons.swift */,
				4DAF33992628F6D900CBEF83 /* UIButton_NavigateToScene.swift */,
				4DC562EC29DE337500D0E820 /* UIButton_RepositionButtons.swift */,
				4DC562F029DE337E00D0E820 /* UIButton_ToggleControls.swift */,
				4DC562E829DE31BF00D0E820 /* UIButton_Back.swift */,
				4DAF33952628F65100CBEF83 /* UIButton_Sound.swift */,
				4DC562F529DE338700D0E820 /* UIButton_Continue.swift */,
				4DC562F629DE338700D0E820 /* UIButton_Purchase.swift */,
				4DC562F429DE338700D0E820 /* UIButton_RestorePurchase.swift */,
				4DD8C15F2E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift */,
				4D12E51326290573006B7624 /* UIButton_Pause.swift */,
				4D25F897262F7ECD00A08FC1 /* UIButton_GameCenter.swift */,
			);
			path = UIButtons;
			sourceTree = "<group>";
		};
		4DC5658125E99A060093BF67 = {
			isa = PBXGroup;
			children = (
				4D95FA332E4EC75C007116F9 /* mark-logo.png */,
				4DC5630129DE341300D0E820 /* sfxButton_back.mp3 */,
				4DC5630029DE341300D0E820 /* sfxButton_play.mp3 */,
				4DC5630229DE341300D0E820 /* sfxButton_select.mp3 */,
				4D94300F2626B879001AB0B7 /* OldCode.txt */,
				4D48AC2F2E4F93DF0065DFB5 /* ToDo.md */,
				4D0AB2C8262F51B8008F2B9A /* Notes.txt */,
				4D0AB2B8262E83B1008F2B9A /* Button Names.txt */,
				4D0F93CA2628B6C8002AE26D /* Prefs.swift */,
				4DA4A5172E34085700178304 /* Constants.swift */,
				4DE4615F26279D500075C7C8 /* Analytics.swift */,
				4D1BD504261FD983006E71ED /* Protocols.swift */,
				4D12E5172629245A006B7624 /* SceneManager.swift */,
				4DAF339D2628F77700CBEF83 /* UIButtons */,
				4D6C88D22624F9BD00E03680 /* Shaders */,
				4D61C86B261D0682009EBA4D /* Sprite Managers */,
				4D61C86A261D0656009EBA4D /* Scenes */,
				4D61C869261D063E009EBA4D /* Sprites */,
				4DC5658625E99A060093BF67 /* Template Shared */,
				4DC5659125E99A070093BF67 /* Template iOS */,
				4DC565A225E99A070093BF67 /* Template tvOS */,
				4DC565B325E99A070093BF67 /* Template macOS */,
				4DC5659025E99A070093BF67 /* Products */,
				4DC5660525E9BC310093BF67 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		4DC5658625E99A060093BF67 /* Template Shared */ = {
			isa = PBXGroup;
			children = (
				4DD8C1632E35B2E2007F4AEF /* BounceAnimation.swift */,
				4D17F2B7262A7FF40073BE22 /* ScoreText.swift */,
				4DC565E125E9B7840093BF67 /* GameData.swift */,
				4D6C88D9262518CF00E03680 /* GameCenter.swift */,
				4D029919261F8CFA0006EB66 /* SKAction-extensions.swift */,
				4D3BB10C261D4F2400E9CCAC /* ui.atlas */,
				4DC5660D25E9BD130093BF67 /* AATools.swift */,
				4DF5A87A26202BAE00F977A9 /* ship3.png */,
				4DC565E825E9B9810093BF67 /* sprite.atlas */,
				4DC5658A25E99A070093BF67 /* Assets.xcassets */,
				4DA4A51C2E34085700178304 /* PurchaseManager.swift */,
				4DA4A5202E34085700178304 /* PurchaseManagerTests.swift */,
			);
			path = "Template Shared";
			sourceTree = "<group>";
		};
		4DC5659025E99A070093BF67 /* Products */ = {
			isa = PBXGroup;
			children = (
				4DC5658F25E99A070093BF67 /* Template.app */,
				4DC565A125E99A070093BF67 /* Template.app */,
				4DC565B225E99A070093BF67 /* Template.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4DC5659125E99A070093BF67 /* Template iOS */ = {
			isa = PBXGroup;
			children = (
				4DC5659225E99A070093BF67 /* AppDelegate.swift */,
				4DC5659425E99A070093BF67 /* GameViewController.swift */,
				4DC5659625E99A070093BF67 /* Main.storyboard */,
				4DC5659925E99A070093BF67 /* LaunchScreen.storyboard */,
				4DC5659C25E99A070093BF67 /* Info.plist */,
			);
			path = "Template iOS";
			sourceTree = "<group>";
		};
		4DC565A225E99A070093BF67 /* Template tvOS */ = {
			isa = PBXGroup;
			children = (
				4DC565A325E99A070093BF67 /* AppDelegate.swift */,
				4DC565A525E99A070093BF67 /* GameViewController.swift */,
				4DC565A725E99A070093BF67 /* Main.storyboard */,
				4DC565AA25E99A070093BF67 /* LaunchScreen.storyboard */,
				4DC565AD25E99A070093BF67 /* Info.plist */,
			);
			path = "Template tvOS";
			sourceTree = "<group>";
		};
		4DC565B325E99A070093BF67 /* Template macOS */ = {
			isa = PBXGroup;
			children = (
				4D01A9D52630FA3C00785EFD /* GameWindow.swift */,
				4DC565B425E99A070093BF67 /* AppDelegate.swift */,
				4DC565B625E99A070093BF67 /* GameViewController.swift */,
				4DC565B825E99A070093BF67 /* Main.storyboard */,
				4DC565BB25E99A070093BF67 /* Info.plist */,
				4DC565BC25E99A070093BF67 /* Template_macOS.entitlements */,
			);
			path = "Template macOS";
			sourceTree = "<group>";
		};
		4DC5660525E9BC310093BF67 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				4D84DD34262F9CC800661737 /* GameController.framework */,
				4D14331E262570BA0001472E /* GameKit.framework */,
				4D6C88D72625169100E03680 /* GameKit.framework */,
				4D6C88D52625169100E03680 /* GameController.framework */,
				4DD41C35261CDCEF00F31FE5 /* GameController.framework */,
				4DD41C33261CDCE800F31FE5 /* GameKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4DC5658E25E99A070093BF67 /* Template iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4DC565CB25E99A070093BF67 /* Build configuration list for PBXNativeTarget "Template iOS" */;
			buildPhases = (
				4DC5658B25E99A070093BF67 /* Sources */,
				4DC5658C25E99A070093BF67 /* Frameworks */,
				4DC5658D25E99A070093BF67 /* Resources */,
				4DC565FB25E9BB4C0093BF67 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Template iOS";
			packageProductDependencies = (
				4D95FA382E4ED773007116F9 /* StoreKitTheKit */,
			);
			productName = "Debris Field iOS";
			productReference = 4DC5658F25E99A070093BF67 /* Template.app */;
			productType = "com.apple.product-type.application";
		};
		4DC565A025E99A070093BF67 /* Template tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4DC565CE25E99A070093BF67 /* Build configuration list for PBXNativeTarget "Template tvOS" */;
			buildPhases = (
				4DC5659D25E99A070093BF67 /* Sources */,
				4DC5659E25E99A070093BF67 /* Frameworks */,
				4DC5659F25E99A070093BF67 /* Resources */,
				4DC5660025E9BB5F0093BF67 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Template tvOS";
			productName = "Debris Field tvOS";
			productReference = 4DC565A125E99A070093BF67 /* Template.app */;
			productType = "com.apple.product-type.application";
		};
		4DC565B125E99A070093BF67 /* Template macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4DC565D125E99A070093BF67 /* Build configuration list for PBXNativeTarget "Template macOS" */;
			buildPhases = (
				4DC565AE25E99A070093BF67 /* Sources */,
				4DC565AF25E99A070093BF67 /* Frameworks */,
				4DC565B025E99A070093BF67 /* Resources */,
				4DC5660825E9BC310093BF67 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Template macOS";
			productName = "Debris Field macOS";
			productReference = 4DC565B225E99A070093BF67 /* Template.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4DC5658225E99A060093BF67 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1240;
				LastUpgradeCheck = 1420;
				TargetAttributes = {
					4DC5658E25E99A070093BF67 = {
						CreatedOnToolsVersion = 12.4;
					};
					4DC565A025E99A070093BF67 = {
						CreatedOnToolsVersion = 12.4;
					};
					4DC565B125E99A070093BF67 = {
						CreatedOnToolsVersion = 12.4;
					};
				};
			};
			buildConfigurationList = 4DC5658525E99A060093BF67 /* Build configuration list for PBXProject "Template" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 4DC5658125E99A060093BF67;
			packageReferences = (
				4D95FA372E4ED773007116F9 /* XCLocalSwiftPackageReference "../../../../../Downloads/StoreKitTheKit-1.5.2" */,
			);
			productRefGroup = 4DC5659025E99A070093BF67 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4DC5658E25E99A070093BF67 /* Template iOS */,
				4DC565A025E99A070093BF67 /* Template tvOS */,
				4DC565B125E99A070093BF67 /* Template macOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4DC5658D25E99A070093BF67 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D9430102626B879001AB0B7 /* OldCode.txt in Resources */,
				4DC565C025E99A070093BF67 /* GameScene.sks in Resources */,
				4DC5630629DE341300D0E820 /* sfxButton_back.mp3 in Resources */,
				4D95FA312E4EA144007116F9 /* PurchaseWaitScene.sks in Resources */,
				4DC5630329DE341300D0E820 /* sfxButton_play.mp3 in Resources */,
				4DC5659825E99A070093BF67 /* Main.storyboard in Resources */,
				4D0AB2B9262E83B1008F2B9A /* Button Names.txt in Resources */,
				4DC5659B25E99A070093BF67 /* LaunchScreen.storyboard in Resources */,
				4D1BD50D261FE11E006E71ED /* retro.fsh in Resources */,
				4D3BB10D261D4F2400E9CCAC /* ui.atlas in Resources */,
				4D42B135262025D200F464D7 /* SHKPassthrough.fsh in Resources */,
				4D0AB2C9262F51B8008F2B9A /* Notes.txt in Resources */,
				4DC565E925E9B9810093BF67 /* sprite.atlas in Resources */,
				4D48AC302E4F93DF0065DFB5 /* ToDo.md in Resources */,
				4DF5A87B26202BAE00F977A9 /* ship3.png in Resources */,
				4D1BD4EB261FB29A006E71ED /* HelpScene.sks in Resources */,
				4DF3CE43262A392C00BA778F /* PositionButtonsScene.sks in Resources */,
				4DC565C625E99A070093BF67 /* Assets.xcassets in Resources */,
				4D95FA362E4EC75C007116F9 /* mark-logo.png in Resources */,
				4D3BB112261D524600E9CCAC /* MenuScene.sks in Resources */,
				4D1BD4F7261FBF69006E71ED /* MoreGamesScene.sks in Resources */,
				4DC5630929DE341300D0E820 /* sfxButton_select.mp3 in Resources */,
				4D01A9DA2631445C00785EFD /* LogoBumperScene.sks in Resources */,
				4D02991E261F904A0006EB66 /* SettingsScene.sks in Resources */,
				4DD95DD4262018D600477598 /* SHKPixelate.fsh in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC5659F25E99A070093BF67 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D9430112626B879001AB0B7 /* OldCode.txt in Resources */,
				4DC565C125E99A070093BF67 /* GameScene.sks in Resources */,
				4DC5630729DE341300D0E820 /* sfxButton_back.mp3 in Resources */,
				4D95FA322E4EA144007116F9 /* PurchaseWaitScene.sks in Resources */,
				4DC5630429DE341300D0E820 /* sfxButton_play.mp3 in Resources */,
				4DC565A925E99A070093BF67 /* Main.storyboard in Resources */,
				4D0AB2BA262E83B1008F2B9A /* Button Names.txt in Resources */,
				4DC565AC25E99A070093BF67 /* LaunchScreen.storyboard in Resources */,
				4D1BD50E261FE11E006E71ED /* retro.fsh in Resources */,
				4D3BB10E261D4F2400E9CCAC /* ui.atlas in Resources */,
				4D42B136262025D200F464D7 /* SHKPassthrough.fsh in Resources */,
				4D0AB2CA262F51B8008F2B9A /* Notes.txt in Resources */,
				4DC565EA25E9B9810093BF67 /* sprite.atlas in Resources */,
				4D48AC312E4F93DF0065DFB5 /* ToDo.md in Resources */,
				4DF5A87C26202BAE00F977A9 /* ship3.png in Resources */,
				4D1BD4EC261FB29A006E71ED /* HelpScene.sks in Resources */,
				4DF3CE52262A3D7600BA778F /* PositionButtonsScene.sks in Resources */,
				4DC565C725E99A070093BF67 /* Assets.xcassets in Resources */,
				4D95FA352E4EC75C007116F9 /* mark-logo.png in Resources */,
				4D3BB113261D524600E9CCAC /* MenuScene.sks in Resources */,
				4D1BD4F8261FBF69006E71ED /* MoreGamesScene.sks in Resources */,
				4DC5630A29DE341300D0E820 /* sfxButton_select.mp3 in Resources */,
				4D01A9DB2631445C00785EFD /* LogoBumperScene.sks in Resources */,
				4D02991F261F904A0006EB66 /* SettingsScene.sks in Resources */,
				4DD95DD5262018D600477598 /* SHKPixelate.fsh in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC565B025E99A070093BF67 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4DD95DD6262018D600477598 /* SHKPixelate.fsh in Resources */,
				4DC5630529DE341300D0E820 /* sfxButton_play.mp3 in Resources */,
				4D42B137262025D200F464D7 /* SHKPassthrough.fsh in Resources */,
				4DC565EB25E9B9810093BF67 /* sprite.atlas in Resources */,
				4D3BB114261D524600E9CCAC /* MenuScene.sks in Resources */,
				4DC5630829DE341300D0E820 /* sfxButton_back.mp3 in Resources */,
				4D9430122626B879001AB0B7 /* OldCode.txt in Resources */,
				4D1BD50F261FE11E006E71ED /* retro.fsh in Resources */,
				4D1BD4F9261FBF69006E71ED /* MoreGamesScene.sks in Resources */,
				4D95FA342E4EC75C007116F9 /* mark-logo.png in Resources */,
				4D029920261F904A0006EB66 /* SettingsScene.sks in Resources */,
				4D01A9DC2631445C00785EFD /* LogoBumperScene.sks in Resources */,
				4DC5630B29DE341300D0E820 /* sfxButton_select.mp3 in Resources */,
				4D1BD4ED261FB29A006E71ED /* HelpScene.sks in Resources */,
				4DF5A87D26202BAE00F977A9 /* ship3.png in Resources */,
				4D3BB10F261D4F2400E9CCAC /* ui.atlas in Resources */,
				4DC565BA25E99A070093BF67 /* Main.storyboard in Resources */,
				4D95FA302E4EA144007116F9 /* PurchaseWaitScene.sks in Resources */,
				4DC565C225E99A070093BF67 /* GameScene.sks in Resources */,
				4D0AB2BB262E83B1008F2B9A /* Button Names.txt in Resources */,
				4D48AC322E4F93DF0065DFB5 /* ToDo.md in Resources */,
				4DC565C825E99A070093BF67 /* Assets.xcassets in Resources */,
				4D0AB2CB262F51B8008F2B9A /* Notes.txt in Resources */,
				4DF3CE53262A3D7600BA778F /* PositionButtonsScene.sks in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		4DC5658B25E99A070093BF67 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D5A1FE626152A55006F96FA /* Rock.swift in Sources */,
				4DC565C325E99A070093BF67 /* GameScene.swift in Sources */,
				4D17F2B8262A7FF40073BE22 /* ScoreText.swift in Sources */,
				4DC565E225E9B7840093BF67 /* GameData.swift in Sources */,
				4DC565DB25E9B7270093BF67 /* Ship.swift in Sources */,
				4DAF339A2628F6D900CBEF83 /* UIButton_NavigateToScene.swift in Sources */,
				4D0AB2C1262E8691008F2B9A /* FoundationScene+MouseInput.swift in Sources */,
				4DE4616026279D500075C7C8 /* Analytics.swift in Sources */,
				4D1BD4EF261FB364006E71ED /* HelpScene.swift in Sources */,
				4D6C88DA262518CF00E03680 /* GameCenter.swift in Sources */,
				4DEEFAD425FDE7CC00264B7C /* MenuScene.swift in Sources */,
				4DA4A5182E34085D00178304 /* Constants.swift in Sources */,
				4DA4A51B2E34085D00178304 /* PurchaseManager.swift in Sources */,
				4DA4A51F2E34085D00178304 /* PurchaseManagerTests.swift in Sources */,
				4D12E51426290573006B7624 /* UIButton_Pause.swift in Sources */,
				4DC5659525E99A070093BF67 /* GameViewController.swift in Sources */,
				4DC562F729DE338700D0E820 /* UIButton_RestorePurchase.swift in Sources */,
				4D5A1FE326152A55006F96FA /* Rocks.swift in Sources */,
				4D0F93CF2628ED71002AE26D /* UIButton_Music.swift in Sources */,
				4DC562FD29DE338700D0E820 /* UIButton_Purchase.swift in Sources */,
				4DC562ED29DE337500D0E820 /* UIButton_RepositionButtons.swift in Sources */,
				4DC5630D29DE34A500D0E820 /* PurchaseWaitScene.swift in Sources */,
				4D12E51C262933D9006B7624 /* VirtualButton.swift in Sources */,
				4DC562E929DE31BF00D0E820 /* UIButton_Back.swift in Sources */,
				4D1BD505261FD983006E71ED /* Protocols.swift in Sources */,
				4DD8C1582E356F02007F4AEF /* UIButton_ResetButtons.swift in Sources */,
				4DC5659325E99A070093BF67 /* AppDelegate.swift in Sources */,
				4DF3CE4F262A3B6000BA778F /* PositionButtonsScene.swift in Sources */,
				4DC5660E25E9BD130093BF67 /* AATools.swift in Sources */,
				4DAF33962628F65100CBEF83 /* UIButton_Sound.swift in Sources */,
				4DC562FA29DE338700D0E820 /* UIButton_Continue.swift in Sources */,
				4D1BD4FB261FBFCA006E71ED /* MoreGamesScene.swift in Sources */,
				4D5A1FDA2613D4F9006F96FA /* Guns.swift in Sources */,
				4D12E5182629245A006B7624 /* SceneManager.swift in Sources */,
				4DDF011A2616ABBB001D8410 /* FoundationScene.swift in Sources */,
				4DD8C1662E35B2ED007F4AEF /* BounceAnimation.swift in Sources */,
				4D0AB2BD262E862C008F2B9A /* Foundation+KeyboardInput.swift in Sources */,
				4DC562F129DE337E00D0E820 /* UIButton_ToggleControls.swift in Sources */,
				4DD95DD826201AB100477598 /* ShaderKitExtensions.swift in Sources */,
				4D25F898262F7ECD00A08FC1 /* UIButton_GameCenter.swift in Sources */,
				4D0F93CB2628B6C8002AE26D /* Prefs.swift in Sources */,
				4D467B2E261D5EBD007C8A26 /* MenuButtonNode.swift in Sources */,
				4D029922261F90F70006EB66 /* SettingsScene.swift in Sources */,
				4DBD99D22631EA0A008B9445 /* LogoBumperScene.swift in Sources */,
				4D02991A261F8CFA0006EB66 /* SKAction-extensions.swift in Sources */,
				4DD8C1602E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift in Sources */,
				4D5A1FD62613D13B006F96FA /* Bullet.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC5659D25E99A070093BF67 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4DC5630E29DE34A500D0E820 /* PurchaseWaitScene.swift in Sources */,
				4DD8C1592E356F02007F4AEF /* UIButton_ResetButtons.swift in Sources */,
				4DC562EE29DE337500D0E820 /* UIButton_RepositionButtons.swift in Sources */,
				4D0AB2BE262E862C008F2B9A /* Foundation+KeyboardInput.swift in Sources */,
				4D5A1FE726152A55006F96FA /* Rock.swift in Sources */,
				4D17F2B9262A7FF40073BE22 /* ScoreText.swift in Sources */,
				4DC565C425E99A070093BF67 /* GameScene.swift in Sources */,
				4DC565E325E9B7840093BF67 /* GameData.swift in Sources */,
				4DC565DC25E9B7270093BF67 /* Ship.swift in Sources */,
				4DC562F229DE337E00D0E820 /* UIButton_ToggleControls.swift in Sources */,
				4DAF339B2628F6D900CBEF83 /* UIButton_NavigateToScene.swift in Sources */,
				4DE4616126279D500075C7C8 /* Analytics.swift in Sources */,
				4DD8C1652E35B2ED007F4AEF /* BounceAnimation.swift in Sources */,
				4D1BD4F0261FB364006E71ED /* HelpScene.swift in Sources */,
				4D6C88DB262518CF00E03680 /* GameCenter.swift in Sources */,
				4DEEFAD525FDE7CC00264B7C /* MenuScene.swift in Sources */,
				4D12E51526290573006B7624 /* UIButton_Pause.swift in Sources */,
				4DC565A625E99A070093BF67 /* GameViewController.swift in Sources */,
				4D25F899262F7ECD00A08FC1 /* UIButton_GameCenter.swift in Sources */,
				4D5A1FE426152A55006F96FA /* Rocks.swift in Sources */,
				4DC562F829DE338700D0E820 /* UIButton_RestorePurchase.swift in Sources */,
				4DC562FE29DE338700D0E820 /* UIButton_Purchase.swift in Sources */,
				4D0F93D02628ED71002AE26D /* UIButton_Music.swift in Sources */,
				4D12E51D262933D9006B7624 /* VirtualButton.swift in Sources */,
				4DC562FB29DE338700D0E820 /* UIButton_Continue.swift in Sources */,
				4D1BD506261FD983006E71ED /* Protocols.swift in Sources */,
				4DA4A5192E34085D00178304 /* Constants.swift in Sources */,
				4DA4A51D2E34085D00178304 /* PurchaseManager.swift in Sources */,
				4DA4A5212E34085D00178304 /* PurchaseManagerTests.swift in Sources */,
				4DC562EA29DE31BF00D0E820 /* UIButton_Back.swift in Sources */,
				4DC565A425E99A070093BF67 /* AppDelegate.swift in Sources */,
				4DC5660F25E9BD130093BF67 /* AATools.swift in Sources */,
				4DAF33972628F65100CBEF83 /* UIButton_Sound.swift in Sources */,
				4D1BD4FC261FBFCA006E71ED /* MoreGamesScene.swift in Sources */,
				4D5A1FDB2613D4F9006F96FA /* Guns.swift in Sources */,
				4D12E5192629245A006B7624 /* SceneManager.swift in Sources */,
				4DDF011B2616ABBB001D8410 /* FoundationScene.swift in Sources */,
				4DBD99D32631EA0A008B9445 /* LogoBumperScene.swift in Sources */,
				4DD95DD926201AB100477598 /* ShaderKitExtensions.swift in Sources */,
				4D0F93CC2628B6C8002AE26D /* Prefs.swift in Sources */,
				4D029918261D63E00006EB66 /* MenuButtonNode.swift in Sources */,
				4D0AB2C2262E8691008F2B9A /* FoundationScene+MouseInput.swift in Sources */,
				4D029923261F90F70006EB66 /* SettingsScene.swift in Sources */,
				4DD8C1612E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift in Sources */,
				4D02991B261F8CFA0006EB66 /* SKAction-extensions.swift in Sources */,
				4D5A1FD72613D13B006F96FA /* Bullet.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DC565AE25E99A070093BF67 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4D0AB2BF262E862C008F2B9A /* Foundation+KeyboardInput.swift in Sources */,
				4D5A1FE826152A55006F96FA /* Rock.swift in Sources */,
				4DC562EB29DE31BF00D0E820 /* UIButton_Back.swift in Sources */,
				4D17F2BA262A7FF40073BE22 /* ScoreText.swift in Sources */,
				4DC565C525E99A070093BF67 /* GameScene.swift in Sources */,
				4DC565E425E9B7840093BF67 /* GameData.swift in Sources */,
				4D01A9D82630FA3C00785EFD /* GameWindow.swift in Sources */,
				4DC565DD25E9B7270093BF67 /* Ship.swift in Sources */,
				4DAF339C2628F6D900CBEF83 /* UIButton_NavigateToScene.swift in Sources */,
				4DE4616226279D500075C7C8 /* Analytics.swift in Sources */,
				4D1BD4F1261FB364006E71ED /* HelpScene.swift in Sources */,
				4DA4A51A2E34085D00178304 /* Constants.swift in Sources */,
				4DA4A51E2E34085D00178304 /* PurchaseManager.swift in Sources */,
				4DA4A5222E34085D00178304 /* PurchaseManagerTests.swift in Sources */,
				4DC5630F29DE34A500D0E820 /* PurchaseWaitScene.swift in Sources */,
				4D6C88DC262518CF00E03680 /* GameCenter.swift in Sources */,
				4DEEFAD625FDE7CC00264B7C /* MenuScene.swift in Sources */,
				4D12E51626290573006B7624 /* UIButton_Pause.swift in Sources */,
				4DC565B725E99A070093BF67 /* GameViewController.swift in Sources */,
				4D25F89A262F7ECD00A08FC1 /* UIButton_GameCenter.swift in Sources */,
				4D5A1FE526152A55006F96FA /* Rocks.swift in Sources */,
				4DC562F929DE338700D0E820 /* UIButton_RestorePurchase.swift in Sources */,
				4D0F93D12628ED71002AE26D /* UIButton_Music.swift in Sources */,
				4D12E51E262933D9006B7624 /* VirtualButton.swift in Sources */,
				4DC562F329DE337E00D0E820 /* UIButton_ToggleControls.swift in Sources */,
				4DD8C15A2E356F02007F4AEF /* UIButton_ResetButtons.swift in Sources */,
				4D1BD507261FD983006E71ED /* Protocols.swift in Sources */,
				4DC565B525E99A070093BF67 /* AppDelegate.swift in Sources */,
				4DC5661025E9BD130093BF67 /* AATools.swift in Sources */,
				4DAF33982628F65100CBEF83 /* UIButton_Sound.swift in Sources */,
				4D1BD4FD261FBFCA006E71ED /* MoreGamesScene.swift in Sources */,
				4D5A1FDC2613D4F9006F96FA /* Guns.swift in Sources */,
				4D12E51A2629245A006B7624 /* SceneManager.swift in Sources */,
				4DC562EF29DE337500D0E820 /* UIButton_RepositionButtons.swift in Sources */,
				4DDF011C2616ABBB001D8410 /* FoundationScene.swift in Sources */,
				4DD8C1642E35B2ED007F4AEF /* BounceAnimation.swift in Sources */,
				4DD95DDA26201AB100477598 /* ShaderKitExtensions.swift in Sources */,
				4D0F93CD2628B6C8002AE26D /* Prefs.swift in Sources */,
				4D029917261D63DF0006EB66 /* MenuButtonNode.swift in Sources */,
				4D0AB2C3262E8691008F2B9A /* FoundationScene+MouseInput.swift in Sources */,
				4DC562FF29DE338700D0E820 /* UIButton_Purchase.swift in Sources */,
				4D029924261F90F70006EB66 /* SettingsScene.swift in Sources */,
				4DBD99D42631EA0A008B9445 /* LogoBumperScene.swift in Sources */,
				4D02991C261F8CFA0006EB66 /* SKAction-extensions.swift in Sources */,
				4DC562FC29DE338700D0E820 /* UIButton_Continue.swift in Sources */,
				4DD8C1622E3574A6007F4AEF /* UIButton_ResetVirtualBtnPositions.swift in Sources */,
				4D5A1FD82613D13B006F96FA /* Bullet.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		4DC5659625E99A070093BF67 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4DC5659725E99A070093BF67 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		4DC5659925E99A070093BF67 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4DC5659A25E99A070093BF67 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		4DC565A725E99A070093BF67 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4DC565A825E99A070093BF67 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		4DC565AA25E99A070093BF67 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4DC565AB25E99A070093BF67 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		4DC565B825E99A070093BF67 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4DC565B925E99A070093BF67 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		4DC565C925E99A070093BF67 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MACOSX_DEPLOYMENT_TARGET = 11.1;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		4DC565CA25E99A070093BF67 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				MACOSX_DEPLOYMENT_TARGET = 11.1;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		4DC565CC25E99A070093BF67 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 2LXSFG44LB;
				INFOPLIST_FILE = "Template iOS/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = games.taara.template;
				PRODUCT_NAME = Template;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		4DC565CD25E99A070093BF67 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 2LXSFG44LB;
				INFOPLIST_FILE = "Template iOS/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = games.taara.template;
				PRODUCT_NAME = Template;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = iphoneos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4DC565CF25E99A070093BF67 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "tvOS App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 54Z7LQPZCG;
				INFOPLIST_FILE = "Template tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sputnikgames.Aerolite;
				PRODUCT_NAME = Template;
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 18.0;
			};
			name = Debug;
		};
		4DC565D025E99A070093BF67 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "tvOS App Icon & Top Shelf Image";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = 54Z7LQPZCG;
				INFOPLIST_FILE = "Template tvOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sputnikgames.Aerolite;
				PRODUCT_NAME = Template;
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 18.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		4DC565D225E99A070093BF67 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_LOCALIZABILITY_EMPTY_CONTEXT = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGN_ENTITLEMENTS = "Template macOS/Template_macOS.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 2LXSFG44LB;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_UNROLL_LOOPS = NO;
				INFOPLIST_FILE = "Template macOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sputnikgames.Aerolite;
				PRODUCT_NAME = Template;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		4DC565D325E99A070093BF67 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ANALYZER_LOCALIZABILITY_EMPTY_CONTEXT = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				CODE_SIGN_ENTITLEMENTS = "Template macOS/Template_macOS.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = 2LXSFG44LB;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_UNROLL_LOOPS = NO;
				INFOPLIST_FILE = "Template macOS/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sputnikgames.Aerolite;
				PRODUCT_NAME = Template;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4DC5658525E99A060093BF67 /* Build configuration list for PBXProject "Template" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DC565C925E99A070093BF67 /* Debug */,
				4DC565CA25E99A070093BF67 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4DC565CB25E99A070093BF67 /* Build configuration list for PBXNativeTarget "Template iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DC565CC25E99A070093BF67 /* Debug */,
				4DC565CD25E99A070093BF67 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4DC565CE25E99A070093BF67 /* Build configuration list for PBXNativeTarget "Template tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DC565CF25E99A070093BF67 /* Debug */,
				4DC565D025E99A070093BF67 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4DC565D125E99A070093BF67 /* Build configuration list for PBXNativeTarget "Template macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DC565D225E99A070093BF67 /* Debug */,
				4DC565D325E99A070093BF67 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		4D95FA372E4ED773007116F9 /* XCLocalSwiftPackageReference "../../../../../Downloads/StoreKitTheKit-1.5.2" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "../../../../../Downloads/StoreKitTheKit-1.5.2";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		4D95FA382E4ED773007116F9 /* StoreKitTheKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = StoreKitTheKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 4DC5658225E99A060093BF67 /* Project object */;
}
